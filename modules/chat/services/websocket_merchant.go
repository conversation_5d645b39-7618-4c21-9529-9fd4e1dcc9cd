/**
 * websocket_merchant.go
 * 商家WebSocket通知功能
 *
 * 本文件实现了商家专用的WebSocket通知功能，包括新订单通知、退款申请处理、
 * 营业状态变更、商品审核结果等商家角色特有的通知需求。
 *
 * 主要功能：
 * 1. 新订单通知
 * 2. 退款申请通知
 * 3. 订单状态变更通知
 * 4. 营业状态通知
 * 5. 商品审核通知
 * 6. 评价和投诉通知
 * 7. 促销活动通知
 */

package services

import (
	"context"
	"fmt"
	"strings"
	"time"

	"o_mall_backend/modules/chat/dto"

	"github.com/beego/beego/v2/core/logs"
)

// WebSocketMerchantService 商家WebSocket通知服务接口
type WebSocketMerchantService interface {
	// 订单相关通知
	SendNewOrderNotification(ctx context.Context, merchantID int64, orderID int64, orderNo string, orderAmount float64, customerInfo map[string]interface{}) error
	SendOrderCancelNotification(ctx context.Context, merchantID int64, orderID int64, orderNo string, cancelReason string) error
	SendOrderStatusUpdateNotification(ctx context.Context, merchantID int64, orderID int64, orderNo string, oldStatus string, newStatus string) error

	// 退款相关通知
	SendRefundRequestNotification(ctx context.Context, merchantID int64, refundID int64, refundNo string, orderID int64, orderNo string, refundAmount float64, refundReason string) error
	SendRefundStatusUpdateNotification(ctx context.Context, merchantID int64, refundID int64, refundNo string, orderID int64, status string, remark string) error

	// 营业状态通知
	SendBusinessStatusNotification(ctx context.Context, merchantID int64, status string, reason string) error
	SendStoreClosingReminder(ctx context.Context, merchantID int64, closingTime time.Time) error
	SendStoreOpeningReminder(ctx context.Context, merchantID int64, openingTime time.Time) error

	// 商品管理通知
	SendProductAuditNotification(ctx context.Context, merchantID int64, productID int64, productName string, auditStatus string, auditRemark string) error
	SendProductStockAlert(ctx context.Context, merchantID int64, productID int64, productName string, currentStock int, minStock int) error
	SendProductOfflineNotification(ctx context.Context, merchantID int64, productID int64, productName string, reason string) error

	// 评价和投诉通知
	SendNewReviewNotification(ctx context.Context, merchantID int64, reviewID int64, orderID int64, rating int, reviewContent string) error
	SendComplaintNotification(ctx context.Context, merchantID int64, complaintID int64, orderID int64, complaintType string, complaintContent string) error

	// 促销活动通知
	SendPromotionStartNotification(ctx context.Context, merchantID int64, promotionID int64, promotionName string, startTime time.Time) error
	SendPromotionEndNotification(ctx context.Context, merchantID int64, promotionID int64, promotionName string, endTime time.Time) error
	SendCouponUsageNotification(ctx context.Context, merchantID int64, couponID int64, couponName string, userID int64, orderID int64) error

	// 财务相关通知
	SendSettlementNotification(ctx context.Context, merchantID int64, settlementID int64, settlementAmount float64, settlementDate time.Time) error
	SendWithdrawalStatusNotification(ctx context.Context, merchantID int64, withdrawalID int64, amount float64, status string) error

	// 系统通知
	SendSystemNotificationToMerchant(ctx context.Context, merchantID int64, title string, content string, notificationType string) error
	SendPolicyUpdateNotification(ctx context.Context, merchantID int64, policyType string, updateContent string, effectiveDate time.Time) error
}

// webSocketMerchantService 商家WebSocket通知服务实现
type webSocketMerchantService struct {
	commonService       WebSocketCommonService
	wsManager           WebSocketManager
	asyncMessageService AsyncMessageService
}

// NewWebSocketMerchantService 创建商家WebSocket通知服务
func NewWebSocketMerchantService() WebSocketMerchantService {
	container := GetServiceContainer()
	return &webSocketMerchantService{
		commonService:       NewWebSocketCommonService(),
		wsManager:           container.GetWebSocketManager(),
		asyncMessageService: container.GetAsyncMessageService(),
	}
}

// SendNewOrderNotification 发送新订单通知
func (s *webSocketMerchantService) SendNewOrderNotification(ctx context.Context, merchantID int64, orderID int64, orderNo string, orderAmount float64, customerInfo map[string]interface{}) error {
	logs.Info("[商家WebSocket服务] 发送新订单通知 - 商家ID: %d, 订单ID: %d, 金额: %.2f", merchantID, orderID, orderAmount)

	data := map[string]interface{}{
		"order_id":          orderID,
		"order_no":          orderNo,
		"order_amount":      orderAmount,
		"customer_info":     customerInfo,
		"notification_type": "new_order",
		"priority":          3, // 高优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "order_detail",
		"action_url":        fmt.Sprintf("/merchant/order/detail/%d", orderID),
		"message":           fmt.Sprintf("您有新的订单 %s，订单金额 %.2f 元，请及时处理", orderNo, orderAmount),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_new_order", data)
}

// SendOrderCancelNotification 发送订单取消通知
func (s *webSocketMerchantService) SendOrderCancelNotification(ctx context.Context, merchantID int64, orderID int64, orderNo string, cancelReason string) error {
	logs.Info("[商家WebSocket服务] 发送订单取消通知 - 商家ID: %d, 订单ID: %d", merchantID, orderID)

	data := map[string]interface{}{
		"order_id":          orderID,
		"order_no":          orderNo,
		"cancel_reason":     cancelReason,
		"notification_type": "order_cancel",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "order_detail",
		"action_url":        fmt.Sprintf("/merchant/order/detail/%d", orderID),
		"message":           fmt.Sprintf("订单 %s 已被取消，取消原因：%s", orderNo, cancelReason),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_order_cancel", data)
}

// SendOrderStatusUpdateNotification 发送订单状态更新通知
func (s *webSocketMerchantService) SendOrderStatusUpdateNotification(ctx context.Context, merchantID int64, orderID int64, orderNo string, oldStatus string, newStatus string) error {
	logs.Info("[商家WebSocket服务] 发送订单状态更新通知 - 商家ID: %d, 订单ID: %d, 状态: %s -> %s", merchantID, orderID, oldStatus, newStatus)

	data := map[string]interface{}{
		"order_id":          orderID,
		"order_no":          orderNo,
		"old_status":        oldStatus,
		"new_status":        newStatus,
		"notification_type": "order_status_update",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "order_detail",
		"action_url":        fmt.Sprintf("/merchant/order/detail/%d", orderID),
		"message":           fmt.Sprintf("订单 %s 状态已更新：%s -> %s", orderNo, oldStatus, newStatus),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_order_status_update", data)
}

// SendRefundRequestNotification 发送退款申请通知
func (s *webSocketMerchantService) SendRefundRequestNotification(ctx context.Context, merchantID int64, refundID int64, refundNo string, orderID int64, orderNo string, refundAmount float64, refundReason string) error {
	logs.Info("[商家WebSocket服务] 发送退款申请通知 - 商家ID: %d, 退款ID: %d, 金额: %.2f", merchantID, refundID, refundAmount)

	data := map[string]interface{}{
		"refund_id":         refundID,
		"refund_no":         refundNo,
		"order_id":          orderID,
		"order_no":          orderNo,
		"refund_amount":     refundAmount,
		"refund_reason":     refundReason,
		"notification_type": "refund_request",
		"priority":          3, // 高优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "refund_process",
		"action_url":        fmt.Sprintf("/merchant/order/detail/%d", orderID),
		"message":           fmt.Sprintf("用户申请退款，订单号：%s，退款金额：%.2f元，退款原因：%s", orderNo, refundAmount, refundReason),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_refund_request", data)
}

// SendRefundStatusUpdateNotification 发送退款状态更新通知
func (s *webSocketMerchantService) SendRefundStatusUpdateNotification(ctx context.Context, merchantID int64, refundID int64, refundNo string, orderID int64, status string, remark string) error {
	logs.Info("[商家WebSocket服务] 发送退款状态更新通知 - 商家ID: %d, 退款ID: %d, 状态: %s", merchantID, refundID, status)

	data := map[string]interface{}{
		"refund_id":         refundID,
		"refund_no":         refundNo,
		"status":            status,
		"remark":            remark,
		"notification_type": "refund_status_update",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "refund_detail",
		"action_url":        fmt.Sprintf("/merchant/order/detail/%d", orderID),
		"message":           fmt.Sprintf("退款 %s 状态已更新：%s", refundNo, status),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_refund_status_update", data)
}

// SendBusinessStatusNotification 发送营业状态通知
func (s *webSocketMerchantService) SendBusinessStatusNotification(ctx context.Context, merchantID int64, status string, reason string) error {
	logs.Info("[商家WebSocket服务] 发送营业状态通知 - 商家ID: %d, 状态: %s", merchantID, status)

	data := map[string]interface{}{
		"business_status":   status,
		"reason":            reason,
		"notification_type": "business_status",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "business_management",
		"message":           fmt.Sprintf("店铺营业状态已更新：%s，原因：%s", status, reason),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_business_status", data)
}

// SendStoreClosingReminder 发送店铺关闭提醒
func (s *webSocketMerchantService) SendStoreClosingReminder(ctx context.Context, merchantID int64, closingTime time.Time) error {
	logs.Info("[商家WebSocket服务] 发送店铺关闭提醒 - 商家ID: %d, 关闭时间: %s", merchantID, closingTime.Format("15:04"))

	data := map[string]interface{}{
		"closing_time":      closingTime.Format("15:04"),
		"notification_type": "store_closing_reminder",
		"priority":          1, // 低优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "business_management",
		"message":           fmt.Sprintf("提醒：您的店铺将在 %s 关闭", closingTime.Format("15:04")),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_store_closing_reminder", data)
}

// SendStoreOpeningReminder 发送店铺开启提醒
func (s *webSocketMerchantService) SendStoreOpeningReminder(ctx context.Context, merchantID int64, openingTime time.Time) error {
	logs.Info("[商家WebSocket服务] 发送店铺开启提醒 - 商家ID: %d, 开启时间: %s", merchantID, openingTime.Format("15:04"))

	data := map[string]interface{}{
		"opening_time":      openingTime.Format("15:04"),
		"notification_type": "store_opening_reminder",
		"priority":          1, // 低优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "business_management",
		"message":           fmt.Sprintf("提醒：您的店铺将在 %s 开启", openingTime.Format("15:04")),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_store_opening_reminder", data)
}

// SendProductAuditNotification 发送商品审核通知
func (s *webSocketMerchantService) SendProductAuditNotification(ctx context.Context, merchantID int64, productID int64, productName string, auditStatus string, auditRemark string) error {
	logs.Info("[商家WebSocket服务] 发送商品审核通知 - 商家ID: %d, 商品ID: %d, 状态: %s", merchantID, productID, auditStatus)

	data := map[string]interface{}{
		"product_id":        productID,
		"product_name":      productName,
		"audit_status":      auditStatus,
		"audit_remark":      auditRemark,
		"notification_type": "product_audit",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "product_detail",
		"action_url":        fmt.Sprintf("/merchant/product/detail/%d", productID),
		"message":           fmt.Sprintf("商品 %s 审核结果：%s", productName, auditStatus),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_product_audit", data)
}

// SendProductStockAlert 发送商品库存告警
func (s *webSocketMerchantService) SendProductStockAlert(ctx context.Context, merchantID int64, productID int64, productName string, currentStock int, minStock int) error {
	logs.Info("[商家WebSocket服务] 发送商品库存告警 - 商家ID: %d, 商品ID: %d, 当前库存: %d", merchantID, productID, currentStock)

	data := map[string]interface{}{
		"product_id":        productID,
		"product_name":      productName,
		"current_stock":     currentStock,
		"min_stock":         minStock,
		"notification_type": "product_stock_alert",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "product_stock",
		"action_url":        fmt.Sprintf("/merchant/product/detail/%d", productID),
		"message":           fmt.Sprintf("商品 %s 库存不足，当前库存：%d，最低库存：%d", productName, currentStock, minStock),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_product_stock_alert", data)
}

// SendProductOfflineNotification 发送商品下架通知
func (s *webSocketMerchantService) SendProductOfflineNotification(ctx context.Context, merchantID int64, productID int64, productName string, reason string) error {
	logs.Info("[商家WebSocket服务] 发送商品下架通知 - 商家ID: %d, 商品ID: %d", merchantID, productID)

	data := map[string]interface{}{
		"product_id":        productID,
		"product_name":      productName,
		"reason":            reason,
		"notification_type": "product_offline",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "product_detail",
		"action_url":        fmt.Sprintf("/merchant/product/detail/%d", productID),
		"message":           fmt.Sprintf("商品 %s 已被下架，原因：%s", productName, reason),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_product_offline", data)
}

// SendNewReviewNotification 发送新评价通知
func (s *webSocketMerchantService) SendNewReviewNotification(ctx context.Context, merchantID int64, reviewID int64, orderID int64, rating int, reviewContent string) error {
	logs.Info("[商家WebSocket服务] 发送新评价通知 - 商家ID: %d, 评价ID: %d, 评分: %d", merchantID, reviewID, rating)

	data := map[string]interface{}{
		"review_id":         reviewID,
		"order_id":          orderID,
		"rating":            rating,
		"review_content":    reviewContent,
		"notification_type": "new_review",
		"priority":          1, // 低优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "review_detail",
		"action_url":        fmt.Sprintf("/merchant/review/detail/%d", reviewID),
		"message":           fmt.Sprintf("您收到了新的评价，评分：%d星", rating),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_new_review", data)
}

// SendComplaintNotification 发送投诉通知
func (s *webSocketMerchantService) SendComplaintNotification(ctx context.Context, merchantID int64, complaintID int64, orderID int64, complaintType string, complaintContent string) error {
	logs.Info("[商家WebSocket服务] 发送投诉通知 - 商家ID: %d, 投诉ID: %d, 类型: %s", merchantID, complaintID, complaintType)

	data := map[string]interface{}{
		"complaint_id":      complaintID,
		"order_id":          orderID,
		"complaint_type":    complaintType,
		"complaint_content": complaintContent,
		"notification_type": "complaint",
		"priority":          3, // 高优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "complaint_detail",
		"action_url":        fmt.Sprintf("/merchant/complaint/detail/%d", complaintID),
		"message":           fmt.Sprintf("您收到了新的投诉，类型：%s", complaintType),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_complaint", data)
}

// SendPromotionStartNotification 发送促销活动开始通知
func (s *webSocketMerchantService) SendPromotionStartNotification(ctx context.Context, merchantID int64, promotionID int64, promotionName string, startTime time.Time) error {
	logs.Info("[商家WebSocket服务] 发送促销活动开始通知 - 商家ID: %d, 活动ID: %d", merchantID, promotionID)

	data := map[string]interface{}{
		"promotion_id":      promotionID,
		"promotion_name":    promotionName,
		"start_time":        startTime.Format("2006-01-02 15:04:05"),
		"notification_type": "promotion_start",
		"priority":          1, // 低优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "promotion_detail",
		"action_url":        fmt.Sprintf("/merchant/promotion/detail/%d", promotionID),
		"message":           fmt.Sprintf("促销活动 %s 已开始", promotionName),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_promotion_start", data)
}

// SendPromotionEndNotification 发送促销活动结束通知
func (s *webSocketMerchantService) SendPromotionEndNotification(ctx context.Context, merchantID int64, promotionID int64, promotionName string, endTime time.Time) error {
	logs.Info("[商家WebSocket服务] 发送促销活动结束通知 - 商家ID: %d, 活动ID: %d", merchantID, promotionID)

	data := map[string]interface{}{
		"promotion_id":      promotionID,
		"promotion_name":    promotionName,
		"end_time":          endTime.Format("2006-01-02 15:04:05"),
		"notification_type": "promotion_end",
		"priority":          1, // 低优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "promotion_detail",
		"action_url":        fmt.Sprintf("/merchant/promotion/detail/%d", promotionID),
		"message":           fmt.Sprintf("促销活动 %s 已结束", promotionName),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_promotion_end", data)
}

// SendCouponUsageNotification 发送优惠券使用通知
func (s *webSocketMerchantService) SendCouponUsageNotification(ctx context.Context, merchantID int64, couponID int64, couponName string, userID int64, orderID int64) error {
	logs.Info("[商家WebSocket服务] 发送优惠券使用通知 - 商家ID: %d, 优惠券ID: %d", merchantID, couponID)

	data := map[string]interface{}{
		"coupon_id":         couponID,
		"coupon_name":       couponName,
		"user_id":           userID,
		"order_id":          orderID,
		"notification_type": "coupon_usage",
		"priority":          1, // 低优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "coupon_detail",
		"action_url":        fmt.Sprintf("/merchant/coupon/detail/%d", couponID),
		"message":           fmt.Sprintf("优惠券 %s 已被使用", couponName),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_coupon_usage", data)
}

// SendSettlementNotification 发送结算通知
func (s *webSocketMerchantService) SendSettlementNotification(ctx context.Context, merchantID int64, settlementID int64, settlementAmount float64, settlementDate time.Time) error {
	logs.Info("[商家WebSocket服务] 发送结算通知 - 商家ID: %d, 结算ID: %d, 金额: %.2f", merchantID, settlementID, settlementAmount)

	data := map[string]interface{}{
		"settlement_id":     settlementID,
		"settlement_amount": settlementAmount,
		"settlement_date":   settlementDate.Format("2006-01-02"),
		"notification_type": "settlement",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "settlement_detail",
		"action_url":        fmt.Sprintf("/merchant/settlement/detail/%d", settlementID),
		"message":           fmt.Sprintf("您有新的结算，金额：%.2f元", settlementAmount),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_settlement", data)
}

// SendWithdrawalStatusNotification 发送提现状态通知
func (s *webSocketMerchantService) SendWithdrawalStatusNotification(ctx context.Context, merchantID int64, withdrawalID int64, amount float64, status string) error {
	logs.Info("[商家WebSocket服务] 发送提现状态通知 - 商家ID: %d, 提现ID: %d, 状态: %s", merchantID, withdrawalID, status)

	data := map[string]interface{}{
		"withdrawal_id":     withdrawalID,
		"amount":            amount,
		"status":            status,
		"notification_type": "withdrawal_status",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "withdrawal_detail",
		"action_url":        fmt.Sprintf("/merchant/withdrawal/detail/%d", withdrawalID),
		"message":           fmt.Sprintf("提现申请状态更新：%s，金额：%.2f元", status, amount),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_withdrawal_status", data)
}

// SendSystemNotificationToMerchant 发送系统通知给商家
func (s *webSocketMerchantService) SendSystemNotificationToMerchant(ctx context.Context, merchantID int64, title string, content string, notificationType string) error {
	logs.Info("[商家WebSocket服务] 发送系统通知 - 商家ID: %d, 类型: %s", merchantID, notificationType)

	data := map[string]interface{}{
		"title":             title,
		"content":           content,
		"notification_type": notificationType,
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "system_notification",
		"message":           content,
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_system_notification", data)
}

// SendPolicyUpdateNotification 发送政策更新通知
func (s *webSocketMerchantService) SendPolicyUpdateNotification(ctx context.Context, merchantID int64, policyType string, updateContent string, effectiveDate time.Time) error {
	logs.Info("[商家WebSocket服务] 发送政策更新通知 - 商家ID: %d, 政策类型: %s", merchantID, policyType)

	data := map[string]interface{}{
		"policy_type":       policyType,
		"update_content":    updateContent,
		"effective_date":    effectiveDate.Format("2006-01-02"),
		"notification_type": "policy_update",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "policy_detail",
		"message":           fmt.Sprintf("政策更新通知：%s，生效日期：%s", policyType, effectiveDate.Format("2006-01-02")),
	}

	return s.sendToMerchant(ctx, merchantID, "merchant_policy_update", data)
}

// sendToMerchant 发送消息给商家
func (s *webSocketMerchantService) sendToMerchant(ctx context.Context, merchantID int64, event string, data map[string]interface{}) error {
	// 检查WebSocket管理器是否已初始化
	if s.wsManager == nil {
		logs.Warn("[商家WebSocket服务] WebSocket管理器未初始化，跳过消息发送")
		return fmt.Errorf("WebSocket管理器未初始化")
	}

	wsMessage := &dto.WebSocketMessageDTO{
		Type:      "notification",
		Event:     event,
		Timestamp: time.Now().Unix(),
		Data:      data,
	}

	err := s.wsManager.SendToAllUserDevices(merchantID, "merchant", wsMessage)
	if err != nil {
		logs.Error("[商家WebSocket服务] 发送通知失败 - 商家ID: %d, 事件: %s, 错误: %v", merchantID, event, err)
		return err
	}

	// 异步保存通知消息到数据库
	if s.asyncMessageService != nil {
		// 从data中提取内容作为消息内容
		content := ""
		if data != nil {
			if title, ok := data["title"].(string); ok {
				content = title
			} else if msg, ok := data["message"].(string); ok {
				content = msg
			} else if content_val, ok := data["content"].(string); ok {
				content = content_val
			}
		}

		// 判断是否为订单相关通知，需要关联到订单通知会话
		var sessionID int64 = 0 // 默认为系统通知，不关联会话
		if data != nil {
			if notificationType, ok := data["notification_type"].(string); ok {
				// 订单相关通知需要关联到订单通知会话
				// 包括：订单通知(order_*)、新订单(new_order)、退款通知(refund_*)
				if strings.HasPrefix(notificationType, "order_") || notificationType == "new_order" || strings.HasPrefix(notificationType, "refund_") {
					// 获取或创建订单通知会话
					if orderSessionID, err := s.getOrCreateOrderNotificationSession(ctx, merchantID, "merchant"); err == nil {
						sessionID = orderSessionID
						logs.Info("[商家WebSocket服务] 订单通知关联到会话 - 商家ID: %d, 会话ID: %d, 通知类型: %s", merchantID, sessionID, notificationType)
					} else {
						logs.Error("[商家WebSocket服务] 获取订单通知会话失败: %v, 商家ID: %d", err, merchantID)
					}
				}
			}
		}

		task := &AsyncMessageTask{
			MessageType: "notification",
			SenderID:    0, // 系统通知发送者ID为0
			SenderType:  "system",
			TargetID:    merchantID,
			TargetType:  "merchant",
			Content:     content,
			WSMessage:   wsMessage,
			NotifyData:  data,
			SessionID:   sessionID, // 设置会话ID
		}

		// 异步保存，不阻塞当前流程
		go func() {
			if err := s.asyncMessageService.SaveNotificationMessageAsync(ctx, task); err != nil {
				logs.Error("[商家WebSocket服务] 异步保存通知消息失败: %v", err)
			}
		}()
	}

	logs.Info("[商家WebSocket服务] 通知发送成功 - 商家ID: %d, 事件: %s", merchantID, event)
	return nil
}

// getOrCreateOrderNotificationSession 获取或创建订单通知会话
func (s *webSocketMerchantService) getOrCreateOrderNotificationSession(ctx context.Context, merchantID int64, userType string) (int64, error) {
	// 通过公共服务获取聊天服务
	container := GetServiceContainer()
	chatService := container.GetChatService()
	if chatService == nil {
		return 0, fmt.Errorf("聊天服务未初始化")
	}

	// 查找或创建订单通知会话
	session, err := chatService.FindOrCreateSession(ctx, 0, "system", merchantID, userType)
	if err != nil {
		return 0, fmt.Errorf("获取订单通知会话失败: %v", err)
	}

	return session.ID, nil
}
