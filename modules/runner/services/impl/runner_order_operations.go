/**
 * runner_order_operations.go
 * 跑腿订单操作相关服务
 *
 * 本文件实现了跑腿订单的各种操作，包括更新状态、接单、取消、取货、完成等功能
 */

package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"

	orderModels "o_mall_backend/modules/order/models"
	"o_mall_backend/modules/runner/constants"
	"o_mall_backend/modules/runner/dto"
	"o_mall_backend/modules/runner/models"
)

// UpdateOrderStatus 更新订单状态和支付状态
func (s *RunnerServiceImpl) UpdateOrderStatus(ctx context.Context, orderID int64, status int, payStatus int, remark string) error {
	// 获取订单信息
	order, err := s.runnerOrderRepo.GetRunnerOrderByID(ctx, orderID)
	if err != nil {
		logs.Error("获取跑腿订单信息失败: %v, 订单ID: %d", err, orderID)
		return errors.New("更新订单状态失败：无法获取订单信息")
	}

	// 更新订单状态
	order.Status = status
	order.PayStatus = payStatus

	// 如果有备注，添加到订单备注中
	if remark != "" {
		if order.Remark != "" {
			order.Remark += "\n"
		}
		order.Remark += remark
	}

	// 保存更新
	err = s.runnerOrderRepo.UpdateRunnerOrder(ctx, order)
	if err != nil {
		logs.Error("更新跑腿订单状态失败: %v, 订单ID: %d", err, orderID)
		return errors.New("更新订单状态失败")
	}

	return nil
}

// AcceptOrder 接单
func (s *RunnerServiceImpl) AcceptOrder(ctx context.Context, req *dto.AcceptOrderRequest, runnerID int64) error {
	// 获取订单信息
	order, err := s.runnerOrderRepo.GetRunnerOrderByID(ctx, req.OrderID)
	if err != nil {
		logs.Error("获取跑腿订单信息失败: %v", err)
		return errors.New("获取订单信息失败")
	}

	// 检查订单状态
	if order.Status != constants.OrderStatusPaid {
		return errors.New("当前订单状态不能接单")
	}

	// 检查订单是否已分配跑腿员
	if order.RunnerID > 0 && order.RunnerID != runnerID {
		return errors.New("该订单已被其他跑腿员接单")
	}

	// 检查跑腿员信息
	runner, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员信息失败: %v", err)
		return errors.New("获取跑腿员信息失败")
	}

	// 检查跑腿员状态
	if runner.Status != 1 {
		return errors.New("您的账号状态异常，无法接单")
	}

	// 更新订单信息
	order.RunnerID = runnerID
	order.Status = constants.OrderStatusAccepted
	order.AcceptTime = time.Now()

	// 保存订单更新
	err = s.runnerOrderRepo.UpdateRunnerOrder(ctx, order)
	if err != nil {
		logs.Error("更新跑腿订单信息失败: %v", err)
		return errors.New("接单失败，请稍后重试")
	}

	// 如果是外卖订单，同步更新外卖订单的配送状态
	if order.OrderType == 2 { // 外卖订单类型，使用正确的常量值
		err = s.syncTakeoutOrderDeliveryStatus(ctx, order.OrderNo, 20) // DeliveryStatusAccepted = 20
		if err != nil {
			logs.Error("同步更新外卖订单配送状态失败: %v", err)
			// 不返回错误，因为主订单处理已成功
		}
	}

	// 更新跑腿员状态
	runner.WorkingStatus = 2 // 配送中
	err = s.runnerRepo.UpdateRunner(ctx, runner)
	if err != nil {
		logs.Error("更新跑腿员状态失败: %v", err)
		// 不返回错误，因为订单已接成功
	}

	return nil
}

// CancelOrder 取消订单
func (s *RunnerServiceImpl) CancelOrder(ctx context.Context, req *dto.CancelOrderRequest, userID int64, userType int) error {
	// 获取订单信息
	order, err := s.runnerOrderRepo.GetRunnerOrderByID(ctx, req.OrderID)
	if err != nil {
		logs.Error("获取跑腿订单信息失败: %v", err)
		return errors.New("获取订单信息失败")
	}

	// 校验权限
	switch userType {
	case constants.UserTypeUser:
		if order.UserID != userID {
			return errors.New("没有权限操作此订单")
		}
	case constants.UserTypeRunner:
		if order.RunnerID != userID {
			return errors.New("没有权限操作此订单")
		}
	default:
		return errors.New("用户类型错误")
	}

	// 检查订单状态
	if order.Status >= constants.OrderStatusCompleted {
		return errors.New("当前订单状态不能取消")
	}

	// 更新订单信息
	order.Status = constants.OrderStatusCanceled
	order.CancelTime = time.Now()
	order.CancelReason = req.Reason
	order.CancelUserType = userType

	// 保存订单更新
	err = s.runnerOrderRepo.UpdateRunnerOrder(ctx, order)
	if err != nil {
		logs.Error("更新跑腿订单信息失败: %v", err)
		return errors.New("取消订单失败，请稍后重试")
	}

	// 如果已支付，执行退款操作
	if order.PayStatus == constants.PayStatusPaid {
		// TODO: 调用退款服务
		logs.Info("订单取消，需要退款，订单号: %s", order.OrderNo)
	}

	// 如果是跑腿员取消，更新跑腿员状态
	if userType == constants.UserTypeRunner {
		runner, err := s.runnerRepo.GetRunnerByID(ctx, userID)
		if err == nil {
			runner.WorkingStatus = 1 // 接单中
			_ = s.runnerRepo.UpdateRunner(ctx, runner)
		}
	}

	return nil
}

// PickupOrder 取货
func (s *RunnerServiceImpl) PickupOrder(ctx context.Context, req *dto.PickupOrderRequest, runnerID int64) error {
	// 获取订单信息
	order, err := s.runnerOrderRepo.GetRunnerOrderByID(ctx, req.OrderID)
	if err != nil {
		logs.Error("获取跑腿订单信息失败: %v", err)
		return errors.New("获取订单信息失败")
	}

	// 校验权限
	if order.RunnerID != runnerID {
		return errors.New("没有权限操作此订单")
	}

	// 检查订单状态
	if order.Status != constants.OrderStatusAccepted {
		return errors.New("当前订单状态不能取货")
	}

	// 更新订单信息
	order.Status = constants.OrderStatusPickedUp
	order.PickupTime = time.Now()

	// 保存订单更新
	err = s.runnerOrderRepo.UpdateRunnerOrder(ctx, order)
	if err != nil {
		logs.Error("更新跑腿订单信息失败: %v", err)
		return errors.New("取货失败，请稍后重试")
	}

	// 如果是外卖订单，同步更新外卖订单的配送状态
	if order.OrderType == 2 { // 外卖订单类型，使用正确的常量值
		err = s.syncTakeoutOrderDeliveryStatus(ctx, order.OrderNo, 40) // DeliveryStatusPickedUp = 40
		if err != nil {
			logs.Error("同步更新外卖订单配送状态失败: %v", err)
			// 不返回错误，因为主订单处理已成功
		}
	}

	// 同步更新主订单状态为处理中/待收货
	err = s.syncOrderStatus(ctx, order.OrderNo, orderModels.OrderStatusProcessing) // OrderStatusProcessing = 30
	if err != nil {
		logs.Error("同步更新主订单状态失败: %v", err)
		// 不返回错误，因为主订单处理已成功
	}

	return nil
}

// StartDelivery 开始配送
func (s *RunnerServiceImpl) StartDelivery(ctx context.Context, req *dto.StartDeliveryRequest, runnerID int64) error {
	// 获取订单信息
	order, err := s.runnerOrderRepo.GetRunnerOrderByID(ctx, req.OrderID)
	if err != nil {
		logs.Error("获取跑腿订单信息失败: %v", err)
		return errors.New("获取订单信息失败")
	}

	// 校验权限
	if order.RunnerID != runnerID {
		return errors.New("没有权限操作此订单")
	}

	// 检查订单状态
	if order.Status != constants.OrderStatusPickedUp {
		return errors.New("当前订单状态不能开始配送")
	}

	// 更新订单信息
	order.DeliveryTime = time.Now() // 记录开始配送时间

	// 保存订单更新
	err = s.runnerOrderRepo.UpdateRunnerOrder(ctx, order)
	if err != nil {
		logs.Error("更新跑腿订单信息失败: %v", err)
		return errors.New("开始配送失败，请稍后重试")
	}

	// 如果是外卖订单，同步更新外卖订单的配送状态
	if order.OrderType == 2 { // 外卖订单类型，使用正确的常量值
		err = s.syncTakeoutOrderDeliveryStatus(ctx, order.OrderNo, 50) // DeliveryStatusDelivering = 50
		if err != nil {
			logs.Error("同步更新外卖订单配送状态失败: %v", err)
			// 不返回错误，因为主订单处理已成功
		}
	}

	// 同步更新主订单状态为配送中
	err = s.syncOrderStatus(ctx, order.OrderNo, orderModels.OrderStatusDelivering) // OrderStatusDelivering = 40
	if err != nil {
		logs.Error("同步更新主订单状态失败: %v", err)
		// 不返回错误，因为主订单处理已成功
	}

	return nil
}

// CompleteOrder 完成订单
func (s *RunnerServiceImpl) CompleteOrder(ctx context.Context, req *dto.CompleteOrderRequest, runnerID int64) error {
	// 获取订单信息
	order, err := s.runnerOrderRepo.GetRunnerOrderByID(ctx, req.OrderID)
	if err != nil {
		logs.Error("获取跑腿订单信息失败: %v", err)
		return errors.New("获取订单信息失败")
	}

	// 校验权限
	if order.RunnerID != runnerID {
		return errors.New("没有权限操作此订单")
	}

	// 检查订单状态
	if order.Status != constants.OrderStatusPickedUp {
		return errors.New("当前订单状态不能完成")
	}

	// 更新订单信息
	order.Status = constants.OrderStatusCompleted
	order.DeliveryTime = time.Now()

	// 保存订单更新
	err = s.runnerOrderRepo.UpdateRunnerOrder(ctx, order)
	if err != nil {
		logs.Error("更新跑腿订单信息失败: %v", err)
		return errors.New("完成订单失败，请稍后重试")
	}

	// 更新跑腿员状态
	runner, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err == nil {
		runner.WorkingStatus = 1 // 接单中
		runner.OrderCount++
		runner.SuccessCount++

		_ = s.runnerRepo.UpdateRunner(ctx, runner)
	}

	// 创建收入记录
	income := &models.RunnerIncomeLog{
		RunnerID:    runnerID,
		OrderID:     order.ID,
		OrderNo:     order.OrderNo,
		Amount:      order.DeliveryFee,
		Type:        0, // 配送费（Type: 0-配送费 1-小费 2-奖励 3-退款）
		Status:      1, // 已入账（Status: 0-未结算 1-已结算 2-已退款）
		Description: fmt.Sprintf("订单配送收入，订单号：%s", order.OrderNo),
	}

	_, err = s.incomeRepo.CreateRunnerIncomeLog(ctx, income)
	if err != nil {
		logs.Error("创建跑腿员收入记录失败: %v", err)
		// 不返回错误，因为订单已完成
	}

	// 更新跑腿员钱包余额
	err = s.runnerMiscRepo.UpdateRunnerWallet(ctx, runnerID, order.DeliveryFee, true) // 添加isAdd参数，为true表示增加余额
	if err != nil {
		logs.Error("更新跑腿员钱包余额失败: %v", err)
		// 不返回错误，因为订单已完成
	}

	// 如果是外卖订单，同步更新外卖订单的配送状态
	if order.OrderType == 1 { // 外卖订单
		err = s.syncTakeoutOrderDeliveryStatus(ctx, order.OrderNo, 60) // DeliveryStatusCompleted = 60
		if err != nil {
			logs.Error("同步更新外卖订单配送状态失败: %v", err)
			// 不返回错误，因为主订单处理已成功
		}
	}

	// 先将主订单状态更新为已送达，然后再更新为已完成
	// 更新为已送达
	err = s.syncOrderStatus(ctx, order.OrderNo, orderModels.OrderStatusDelivered) // OrderStatusDelivered = 45
	if err != nil {
		logs.Error("同步更新主订单状态为已送达失败: %v", err)
		// 不返回错误，因为主订单处理已成功
	}

	// 延迟一小段时间后再更新为已完成状态
	// 此处不延迟，直接更新为已完成状态
	// 实际项目中可能需要加入延迟逻辑或通过定时任务实现
	err = s.syncOrderStatus(ctx, order.OrderNo, orderModels.OrderStatusCompleted) // OrderStatusCompleted = 50
	if err != nil {
		logs.Error("同步更新主订单状态为已完成失败: %v", err)
		// 不返回错误，因为主订单处理已成功
	}

	return nil
}

// UserRateOrder 用户评价订单
func (s *RunnerServiceImpl) UserRateOrder(ctx context.Context, req *dto.RateOrderRequest, userID int64) error {
	// 获取订单信息
	order, err := s.runnerOrderRepo.GetRunnerOrderByID(ctx, req.OrderID)
	if err != nil {
		logs.Error("获取跑腿订单信息失败: %v", err)
		return errors.New("获取订单信息失败")
	}

	// 校验权限
	if order.UserID != userID {
		return errors.New("没有权限操作此订单")
	}

	// 检查订单状态
	if order.Status != constants.OrderStatusCompleted {
		return errors.New("只能评价已完成的订单")
	}

	// 检查是否已评价
	if order.ScoreByUser > 0 {
		return errors.New("已评价过此订单")
	}

	// 更新订单评价信息
	order.ScoreByUser = req.Score
	order.CommentByUser = req.Comment

	// 保存订单更新
	err = s.runnerOrderRepo.UpdateRunnerOrder(ctx, order)
	if err != nil {
		logs.Error("更新跑腿订单评价信息失败: %v", err)
		return errors.New("评价订单失败，请稍后重试")
	}

	// 更新跑腿员评分
	if order.RunnerID > 0 {
		// 获取跑腿员所有评分的平均值
		avgScore, err := s.runnerOrderRepo.GetRunnerAverageScore(ctx, order.RunnerID)
		if err != nil {
			logs.Error("获取跑腿员平均评分失败: %v", err)
		} else {
			// 更新跑腿员评分
			runner, err := s.runnerRepo.GetRunnerByID(ctx, order.RunnerID)
			if err == nil {
				runner.Score = avgScore
				_ = s.runnerRepo.UpdateRunner(ctx, runner)
			}
		}
	}

	return nil
}

// RunnerRateOrder 跑腿员评价订单
func (s *RunnerServiceImpl) RunnerRateOrder(ctx context.Context, req *dto.RateOrderRequest, runnerID int64) error {
	// 获取订单信息
	order, err := s.runnerOrderRepo.GetRunnerOrderByID(ctx, req.OrderID)
	if err != nil {
		logs.Error("获取跑腿订单信息失败: %v", err)
		return errors.New("获取订单信息失败")
	}

	// 校验权限
	if order.RunnerID != runnerID {
		return errors.New("没有权限操作此订单")
	}

	// 检查订单状态
	if order.Status != constants.OrderStatusCompleted {
		return errors.New("只能评价已完成的订单")
	}

	// 检查是否已评价
	if order.ScoreByRunner > 0 {
		return errors.New("已评价过此订单")
	}

	// 更新订单评价信息
	order.ScoreByRunner = req.Score
	order.CommentByRunner = req.Comment

	// 保存订单更新
	err = s.runnerOrderRepo.UpdateRunnerOrder(ctx, order)
	if err != nil {
		logs.Error("更新跑腿订单评价信息失败: %v", err)
		return errors.New("评价订单失败，请稍后重试")
	}

	return nil
}

// CalculateDeliveryFee 计算配送费
func (s *RunnerServiceImpl) CalculateDeliveryFee(ctx context.Context, req *dto.DeliveryFeeCalculateRequest) (*dto.DeliveryFeeCalculateResponse, error) {
	// 计算距离
	distance := calculateDistance(req.PickupLat, req.PickupLng, req.DeliveryLat, req.DeliveryLng)

	// 基础配送费规则
	var baseDeliveryFee float64
	if distance <= 3 {
		// 3公里内固定价格
		baseDeliveryFee = 8.0
	} else {
		// 超过3公里，每公里增加2元
		baseDeliveryFee = 8.0 + (distance-3)*2.0
	}

	// 重量附加费
	var weightFee float64
	if req.GoodsWeight > 5 {
		// 超过5公斤，每公斤增加1元
		weightFee = (req.GoodsWeight - 5) * 1.0
	}

	// 服务费，基本为配送费的10%
	serviceFee := (baseDeliveryFee + weightFee) * 0.1

	// 总配送费
	totalDeliveryFee := baseDeliveryFee + weightFee

	// 订单类型附加费
	if req.OrderType == constants.OrderTypeUrgent {
		// 加急订单增加50%费用
		totalDeliveryFee = totalDeliveryFee * 1.5
		serviceFee = serviceFee * 1.5
	} else if req.OrderType == constants.OrderTypeSpecial {
		// 特殊订单增加30%费用
		totalDeliveryFee = totalDeliveryFee * 1.3
		serviceFee = serviceFee * 1.3
	}

	// 预计配送时间，基础10分钟，每公里增加5分钟
	estimateTime := 10 + int(distance*5)

	// 构建响应
	resp := &dto.DeliveryFeeCalculateResponse{
		Distance:         distance,
		BaseDeliveryFee:  baseDeliveryFee,
		WeightFee:        weightFee,
		TotalDeliveryFee: totalDeliveryFee,
		ServiceFee:       serviceFee,
		EstimateTime:     estimateTime,
	}

	return resp, nil
}

// 获取订单类型描述
func getOrderTypeDesc(orderType int) string {
	switch orderType {
	case constants.OrderTypeNormal:
		return "普通订单"
	case constants.OrderTypeUrgent:
		return "加急订单"
	case constants.OrderTypeSpecial:
		return "特殊订单"
	default:
		return "未知类型"
	}
}

// 获取订单状态描述
func getOrderStatusDesc(status int) string {
	switch status {
	case constants.OrderStatusWaitingPay:
		return "待支付"
	case constants.OrderStatusPaid:
		return "待接单"
	case constants.OrderStatusAccepted:
		return "待取货"
	case constants.OrderStatusPickedUp:
		return "配送中"
	case constants.OrderStatusCompleted:
		return "已完成"
	case constants.OrderStatusCanceled:
		return "已取消"
	default:
		return "未知状态"
	}
}

// 获取支付状态描述
func getPayStatusDesc(status int) string {
	switch status {
	case constants.PayStatusUnpaid:
		return "未支付"
	case constants.PayStatusPaid:
		return "已支付"
	case constants.PayStatusRefund:
		return "已退款"
	default:
		return "未知状态"
	}
}

// 获取支付方式描述
func getPayMethodDesc(method int) string {
	switch method {
	case constants.PayMethodWeChat:
		return "微信支付"
	case constants.PayMethodAliPay:
		return "支付宝"
	case constants.PayMethodBalance:
		return "余额支付"
	default:
		return "未知方式"
	}
}
