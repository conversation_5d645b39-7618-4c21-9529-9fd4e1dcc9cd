/**
 * takeout_auto_assign.go
 * 外卖订单自动分配骑手相关功能
 *
 * 本文件实现了外卖订单自动分配骑手的核心功能，包括:
 * 1. 自动分配骑手的算法实现
 * 2. 骑手评分计算
 * 3. 分配结果处理
 * 4. 集成商家位置和骑手位置服务
 */

package services

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"sort"
	"sync"
	"time"

	"github.com/beego/beego/v2/core/logs"

	merchantSvc "o_mall_backend/modules/merchant/services" // 导入商家位置服务
	runnerSvc "o_mall_backend/modules/runner/services"     // 导入骑手位置服务
	"o_mall_backend/modules/takeout/models"
	"o_mall_backend/utils/constants"
)

// MerchantLocation 商家位置信息
type MerchantLocation struct {
	MerchantID int64   // 商家ID
	Latitude   float64 // 纬度
	Longitude  float64 // 经度
}

// NearbyRunnerResponse 附近骑手信息
type NearbyRunnerResponse struct {
	RunnerID      int64   // 骑手ID
	Name          string  // 骑手名称
	RealName      string  // 骑手真实姓名
	Mobile        string  // 手机号
	Latitude      float64 // 纬度
	Longitude     float64 // 经度
	Distance      float64 // 距离(公里)
	Rating        float64 // 评分
	OrderCount    int     // 当前订单数量
	WorkingStatus int     // 工作状态 1:正常 0:休息
}

// NearbyRunnerRequest 附近骑手查询请求
type NearbyRunnerRequest struct {
	Latitude  float64 // 纬度
	Longitude float64 // 经度
	Radius    float64 // 半径(公里)
	Limit     int     // 限制返回数量
}

// 自动分配配置
type AutoAssignConfig struct {
	Enabled        bool    `json:"enabled"`         // 是否启用自动分配
	RadiusKm       float64 `json:"radius_km"`       // 搜索半径(公里)
	MaxAttempts    int     `json:"max_attempts"`    // 最大尝试次数
	DistanceWeight float64 `json:"distance_weight"` // 距离权重
	ScoreWeight    float64 `json:"score_weight"`    // 评分权重
	WorkloadWeight float64 `json:"workload_weight"` // 工作量权重
}

// GetAutoAssignConfig 获取自动分配配置（已废弃，请使用GetTakeoutConfigService）
func GetAutoAssignConfig() AutoAssignConfig {
	// 从配置服务获取
	cfgSvc := GetTakeoutConfigService()
	config, err := cfgSvc.GetAutoAssignConfig(context.Background())
	if err != nil {
		logs.Error("获取自动分配配置失败: %v，使用默认配置", err)
		// 使用默认值
		return AutoAssignConfig{
			Enabled:        true, // 默认启用
			RadiusKm:       5.0,  // 默认5公里
			MaxAttempts:    3,    // 默认最多尝试3次
			DistanceWeight: 0.5,  // 距离权重
			ScoreWeight:    0.3,  // 评分权重
			WorkloadWeight: 0.2,  // 工作量权重
		}
	}
	return config
}

// 骑手分配结果
type RunnerAssignResult struct {
	Success      bool      // 是否成功
	RunnerID     int64     // 分配的骑手ID
	RunnerName   string    // 骑手姓名
	RunnerMobile string    // 骑手手机
	Message      string    // 结果消息
	Timestamp    time.Time // 分配时间
}

// 自动分配锁，避免并发分配
var autoAssignMutex sync.Mutex

// AssignDeliveryWithRetry 尝试多次分配骑手
func (s *takeoutOrderService) AssignDeliveryWithRetry(ctx context.Context, orderID, runnerID int64) error {
	// 最多重试分配的次数
	maxRetries := 3
	var lastErr error

	for i := 0; i < maxRetries; i++ {
		// 尝试分配
		// 使用系统操作者ID，后续可从TakeoutConfig中获取
		sysOperatorID := int64(0)
		err := s.AssignDelivery(orderID, runnerID, sysOperatorID)

		if err == nil {
			// 分配成功
			logs.Info("[自动分配] 订单 %d 分配成功至骑手 %d", orderID, runnerID)
			return nil
		}

		// 记录错误并重试
		logs.Warn("[自动分配] 订单 %d 分配骑手 %d 失败，尝试第 %d 次: %v",
			orderID, runnerID, i+1, err)
		lastErr = err

		// 等待一段时间再重试
		time.Sleep(time.Duration(500+rand.Intn(500)) * time.Millisecond)
	}

	return fmt.Errorf("多次尝试分配失败，最后错误: %v", lastErr)
}

// AutoAssignDelivery 自动分配配送员
// 根据订单信息和配置，自动为指定订单查询并选择最合适的骑手进行分配
func (s *takeoutOrderService) AutoAssignDelivery(ctx context.Context, orderID int64) error {
	logs.Info("[自动分配骑手] 开始为订单 %d 分配骑手", orderID)

	// 避免并发分配
	autoAssignMutex.Lock()
	defer autoAssignMutex.Unlock()

	// 获取订单信息
	order, err := s.baseOrderSvc.GetOrder(ctx, orderID)
	if err != nil {
		logs.Error("[自动分配骑手] 获取订单信息失败: %v, 订单ID: %d", err, orderID)
		return fmt.Errorf("获取订单信息失败: %w", err)
	}

	// 检查订单是否存在
	if order == nil {
		errmsg := fmt.Sprintf("[自动分配骑手] 订单不存在, 订单ID: %d", orderID)
		logs.Error(errmsg)
		return errors.New(errmsg)
	}

	// 检查订单类型
	if order.OrderType != constants.OrderTypeTakeout { // 外卖订单类型，使用统一常量
		return errors.New("非外卖订单不能分配骑手")
	}

	// 获取外卖订单扩展信息
	takeoutOrder, err := s.orderRepo.GetOrderExtensionByOrderID(orderID)
	if err != nil {
		logs.Error("[自动分配骑手] 获取外卖订单扩展信息失败: %v, 订单ID: %d", err, orderID)
		return err
	}

	// 检查订单状态是否允许分配
	// 注意：这里的状态检查移到上面的order.OrderType检查中进行了

	// 检查订单配送状态 - 只有已接单的订单可以分配骑手
	if takeoutOrder.DeliveryStatus != DeliveryStatusAccepted {
		errmsg := fmt.Sprintf("订单配送状态不允许分配骑手, 当前状态: %d", takeoutOrder.DeliveryStatus)
		logs.Warn("[自动分配骑手] %s, 订单ID: %d", errmsg, orderID)
		return errors.New(errmsg)
	}

	// 获取配置
	config := GetAutoAssignConfig()
	if !config.Enabled {
		logs.Info("[自动分配骑手] 自动分配功能未启用")
		return nil
	}

	// 获取外卖订单扩展信息
	takeoutOrder, err = s.orderRepo.GetOrderExtensionByOrderID(orderID)
	if err != nil {
		logs.Error("[自动分配骑手] 获取外卖订单扩展信息失败: %v, 订单ID: %d", err, orderID)
		return err
	}

	// 检查配送状态，只有待配送状态可以分配骑手
	// 注意: 我们在前面已经检查了 DeliveryStatusWaiting，这里删除重复检查

	// 检查是否已有分配尝试次数超限
	if takeoutOrder.AssignAttempts >= config.MaxAttempts {
		logs.Info("[自动分配骑手] 订单分配尝试次数已达上限: %d, 订单ID: %d", takeoutOrder.AssignAttempts, orderID)
		return errors.New("订单分配尝试次数已达上限")
	}

	// 获取商家信息
	merchantInfo, err := s.getMerchantInfo(ctx, takeoutOrder.MerchantID)
	if err != nil {
		logs.Error("[自动分配骑手] 获取商家信息失败: %v, 商家ID: %d", err, takeoutOrder.MerchantID)
		return err
	}

	// 查询附近骑手
	nearbyRunners, err := s.queryNearbyRunners(ctx, merchantInfo.Latitude, merchantInfo.Longitude, config.RadiusKm)
	if err != nil {
		logs.Error("[自动分配骑手] 查询附近骑手失败: %v", err)
		s.recordAssignAttempt(orderID, takeoutOrder, false, 0, "", "", "查询附近骑手失败")
		return err
	}

	// 没有找到合适骑手
	if len(nearbyRunners) == 0 {
		logs.Info("[自动分配骑手] 附近没有可用骑手, 商家位置: [%f, %f], 半径: %f公里",
			merchantInfo.Latitude, merchantInfo.Longitude, config.RadiusKm)
		s.recordAssignAttempt(orderID, takeoutOrder, false, 0, "", "", "附近没有可用骑手")
		return errors.New("附近没有可用骑手")
	}

	// 为每个骑手计算得分并排序
	scoredRunners := s.scoreRunners(nearbyRunners, merchantInfo.Latitude, merchantInfo.Longitude, config)

	// 没有合格的骑手
	if len(scoredRunners) == 0 {
		logs.Info("[自动分配骑手] 没有找到合格的骑手")
		s.recordAssignAttempt(orderID, takeoutOrder, false, 0, "", "", "没有找到合格的骑手")
		return errors.New("没有找到合格的骑手")
	}

	// 选择评分最高的骑手
	selectedRunner := scoredRunners[0]
	logs.Info("[自动分配骑手] 选择骑手: ID=%d, 姓名=%s, 得分=%.2f",
		selectedRunner.Runner.RunnerID, selectedRunner.Runner.RealName, selectedRunner.Score)

	// 分配骑手
	// 使用系统操作者ID
	sysOperatorID := int64(0)
	err = s.assignDeliveryToRunner(ctx, orderID, selectedRunner.Runner.RunnerID, sysOperatorID)
	if err != nil {
		logs.Error("[自动分配骑手] 分配骑手失败: %v, 骑手ID: %d", err, selectedRunner.Runner.RunnerID)
		s.recordAssignAttempt(orderID, takeoutOrder, false, selectedRunner.Runner.RunnerID,
			selectedRunner.Runner.RealName, selectedRunner.Runner.Mobile, "分配骑手失败: "+err.Error())
		return err
	}

	// 记录成功分配
	s.recordAssignAttempt(orderID, takeoutOrder, true, selectedRunner.Runner.RunnerID,
		selectedRunner.Runner.RealName, selectedRunner.Runner.Mobile, "自动分配骑手成功")

	logs.Info("[自动分配骑手] 成功为订单 %d 分配骑手 %d(%s)", orderID, selectedRunner.Runner.RunnerID, selectedRunner.Runner.RealName)
	return nil
}

// 查询附近骑手
func (s *takeoutOrderService) queryNearbyRunners(ctx context.Context, latitude, longitude, radiusKm float64) ([]NearbyRunnerResponse, error) {
	// 使用骑手位置服务查询附近骑手
	runnerLocSvc := runnerSvc.NewRunnerLocationService()

	// 设置最大返回数量
	limit := 20

	// 调用骑手位置服务查询附近骑手
	nearbyRunners, err := runnerLocSvc.FindNearbyRunners(ctx, latitude, longitude, radiusKm, limit)
	if err != nil {
		logs.Error("查询附近骑手失败: %v, 坐标: [%f, %f], 半径: %f", err, latitude, longitude, radiusKm)
		return nil, fmt.Errorf("查询附近骑手失败: %w", err)
	}

	if len(nearbyRunners) == 0 {
		logs.Warning("未找到附近骑手, 坐标: [%f, %f], 半径: %f", latitude, longitude, radiusKm)
		return []NearbyRunnerResponse{}, nil
	}

	// 转换为内部模型格式
	result := make([]NearbyRunnerResponse, 0, len(nearbyRunners))
	for _, runner := range nearbyRunners {
		result = append(result, NearbyRunnerResponse{
			RunnerID:      runner.RunnerID,
			Name:          runner.RealName, // 使用真实姓名作为骑手名称
			RealName:      runner.RealName,
			Mobile:        runner.Mobile,
			Latitude:      runner.Latitude,
			Longitude:     runner.Longitude,
			Distance:      runner.Distance,
			Rating:        runner.Score, // 使用评分
			OrderCount:    runner.OrderCount,
			WorkingStatus: runner.WorkingStatus,
		})
	}

	logs.Info("查询附近骑手成功, 找到%d位骑手", len(result))
	return result, nil
}

// 骑手评分对象
type ScoredRunner struct {
	Runner NearbyRunnerResponse // 骑手信息
	Score  float64              // 总分
}

// 为骑手评分并排序
func (s *takeoutOrderService) scoreRunners(runners []NearbyRunnerResponse,
	merchantLat, merchantLng float64, config AutoAssignConfig) []ScoredRunner {

	scoredRunners := make([]ScoredRunner, 0, len(runners))

	for _, runner := range runners {
		// 仅选择正在接单中(状态为1)的骑手
		if runner.WorkingStatus != 1 {
			continue
		}

		// 计算距离得分 - 越近评分越高
		distanceScore := 1.0
		if runner.Distance > 0 {
			distanceScore = 1.0 / runner.Distance
		}

		// 评分得分 - 越高评分越高
		ratingScore := runner.Rating / 5.0 // 假设满分为5分

		// 工作量得分 - 工作量越小评分越高
		workloadScore := 1.0
		if runner.OrderCount > 0 {
			workloadScore = 1.0 / float64(runner.OrderCount+1)
		}

		// 计算加权总分
		totalScore := (distanceScore * config.DistanceWeight) +
			(ratingScore * config.ScoreWeight) +
			(workloadScore * config.WorkloadWeight)

		scored := ScoredRunner{
			Runner: runner,
			Score:  totalScore,
		}

		scoredRunners = append(scoredRunners, scored)
	}

	// 按分数从高到低排序
	sort.Slice(scoredRunners, func(i, j int) bool {
		return scoredRunners[i].Score > scoredRunners[j].Score
	})

	return scoredRunners
}

// 获取商家信息
func (s *takeoutOrderService) getMerchantInfo(ctx context.Context, merchantID int64) (*MerchantLocation, error) {
	// 从商家位置服务获取商家位置
	merchantLocSvc := merchantSvc.NewMerchantLocationService()
	locInfo, err := merchantLocSvc.GetMerchantLocation(ctx, merchantID)
	if err != nil {
		logs.Error("获取商家位置失败: %v, 商家ID: %d", err, merchantID)
		return nil, fmt.Errorf("获取商家位置失败: %w", err)
	}

	// 将商家位置信息转换为内部模型
	return &MerchantLocation{
		MerchantID: merchantID,
		Latitude:   locInfo.Latitude,
		Longitude:  locInfo.Longitude,
	}, nil
}

// 获取商家位置
func (s *takeoutOrderService) getMerchantLocation(ctx context.Context, merchantID int64) (float64, float64, error) {
	// 使用商家位置服务获取商家位置
	merchantInfo, err := s.getMerchantInfo(ctx, merchantID)
	if err != nil {
		return 0, 0, err
	}

	return merchantInfo.Latitude, merchantInfo.Longitude, nil
}

// 分配骑手并更新订单信息
func (s *takeoutOrderService) assignDeliveryToRunner(ctx context.Context, orderID, runnerID, operatorID int64) error {
	// 获取骑手信息
	runnerLocSvc := runnerSvc.NewRunnerLocationService()

	// 获取骑手位置信息，确保骑手存在
	_, err := runnerLocSvc.GetRunnerLocation(ctx, runnerID)
	if err != nil {
		logs.Error("获取骑手信息失败: %v, 骑手ID: %d", err, runnerID)
		return fmt.Errorf("获取骑手信息失败: %w", err)
	}

	// 调用现有分配方法
	err = s.AssignDelivery(orderID, runnerID, operatorID)
	if err != nil {
		logs.Error("分配骑手失败: %v, 订单ID: %d, 骑手ID: %d", err, orderID, runnerID)
		return fmt.Errorf("分配骑手失败: %w", err)
	}

	logs.Info("成功分配骑手, 订单ID: %d, 骑手ID: %d, 操作者ID: %d", orderID, runnerID, operatorID)
	return nil
}

// 记录分配尝试
// 分配指标统计信息，用于记录和监控自动分配效果
var (
	assignMetrics = struct {
		sync.Mutex
		totalAttempts   int            // 总尝试次数
		successAttempts int            // 成功次数
		failureAttempts int            // 失败次数
		totalDuration   int            // 总耗时（秒）
		avgDuration     float64        // 平均耗时（秒）
		failureReasons  map[string]int // 失败原因统计
	}{
		failureReasons: make(map[string]int),
	}
)

// recordAssignmentMetrics 记录分配指标，用于统计和监控自动分配效果
func (s *takeoutOrderService) recordAssignmentMetrics(result string, duration, attempts int, reason string) {
	assignMetrics.Lock()
	defer assignMetrics.Unlock()

	assignMetrics.totalAttempts++

	switch result {
	case "success":
		assignMetrics.successAttempts++
		assignMetrics.totalDuration += duration
		if assignMetrics.successAttempts > 0 {
			assignMetrics.avgDuration = float64(assignMetrics.totalDuration) / float64(assignMetrics.successAttempts)
		}

		// 打印当前统计信息
		logs.Info("[自动分配统计] 成功率: %.2f%%, 总尝试: %d, 成功: %d, 失败: %d, 平均耗时: %.2f秒",
			float64(assignMetrics.successAttempts)*100/float64(assignMetrics.totalAttempts),
			assignMetrics.totalAttempts,
			assignMetrics.successAttempts,
			assignMetrics.failureAttempts,
			assignMetrics.avgDuration)

	case "failure":
		assignMetrics.failureAttempts++

		// 统计失败原因
		shortReason := reason
		if len(shortReason) > 30 {
			shortReason = shortReason[:30]
		}
		assignMetrics.failureReasons[shortReason]++

		// 每10次失败打印一次失败统计
		if assignMetrics.failureAttempts%10 == 0 {
			reasons := "失败原因统计:\n"
			for r, count := range assignMetrics.failureReasons {
				reasons += fmt.Sprintf("- %s: %d次\n", r, count)
			}
			logs.Warning("[自动分配统计] 失败率: %.2f%%, 总尝试: %d, 成功: %d, 失败: %d\n%s",
				float64(assignMetrics.failureAttempts)*100/float64(assignMetrics.totalAttempts),
				assignMetrics.totalAttempts,
				assignMetrics.successAttempts,
				assignMetrics.failureAttempts,
				reasons)
		}
	}

	// TODO: 这里可以将指标数据定期保存到Redis或者导出到Prometheus等监控系统
}

func (s *takeoutOrderService) recordAssignAttempt(orderID int64, takeoutOrder *models.TakeoutOrderExtension,
	success bool, runnerID int64, runnerName string, runnerMobile string, message string) {

	// 更新分配统计信息
	now := time.Now()
	takeoutOrder.AssignAttempts++
	takeoutOrder.LastAssignTime = now

	// 记录分配结果
	if success {
		takeoutOrder.AutoAssigned = 1
		// 设置骑手信息
		takeoutOrder.DeliveryStaffID = runnerID
		takeoutOrder.DeliveryStaffName = runnerName
		takeoutOrder.DeliveryStaffPhone = runnerMobile
	}

	// 计算订单分配耗时
	assignDuration := int(now.Sub(takeoutOrder.CreatedAt).Seconds())

	// 保存更新
	err := s.orderRepo.UpdateOrderExtension(takeoutOrder)
	if err != nil {
		logs.Error("[自动分配骑手] 更新分配尝试记录失败: %v, 订单ID: %d", err, orderID)
		return
	}

	// 记录订单日志
	var action string
	var remark string
	if success {
		action = "auto_assign_success"
		remark = fmt.Sprintf("系统自动分配骑手成功: %s (手机: %s), 尝试次数: %d, 耗时: %d秒",
			runnerName, runnerMobile, takeoutOrder.AssignAttempts, assignDuration)
	} else {
		action = "auto_assign_fail"
		remark = fmt.Sprintf("系统自动分配骑手失败: %s, 尝试次数: %d",
			message, takeoutOrder.AssignAttempts)
	}

	log := &models.TakeoutOrderLog{
		OrderID: orderID,
		UserID:  0, // 系统操作
		Action:  action,
		Remark:  remark,
		IP:      "",
	}

	_, err = s.orderRepo.CreateOrderLog(log)
	if err != nil {
		logs.Error("[自动分配骑手] 创建订单日志失败: %v, 订单ID: %d", err, orderID)
	}

	// 记录分配指标
	if success {
		// 打印详细的成功分配日志
		logs.Info("[自动分配指标] 订单 %d 分配成功, 骑手: %d(%s), 尝试次数: %d, 耗时: %d秒",
			orderID, runnerID, runnerName, takeoutOrder.AssignAttempts, assignDuration)

		// 记录成功统计信息
		s.recordAssignmentMetrics("success", assignDuration, takeoutOrder.AssignAttempts, message)
	} else {
		logs.Warning("[自动分配指标] 订单 %d 分配失败, 原因: %s, 尝试次数: %d",
			orderID, message, takeoutOrder.AssignAttempts)

		// 记录失败统计信息
		s.recordAssignmentMetrics("failure", 0, takeoutOrder.AssignAttempts, message)
	}
}
