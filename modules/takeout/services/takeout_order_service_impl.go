/**
 * 外卖订单服务实现补充方法
 *
 * 本文件实现了外卖订单服务接口中的补充方法，包括商家订单统计等功能。
 */

package services

import (
	"context"
	"fmt"
	"o_mall_backend/common/result"
	chatServices "o_mall_backend/modules/chat/services"
	orderConstants "o_mall_backend/modules/order/constants"
	orderDto "o_mall_backend/modules/order/dto"
	paymentDto "o_mall_backend/modules/payment/dto"
	paymentModels "o_mall_backend/modules/payment/models"
	paymentServices "o_mall_backend/modules/payment/services"
	paymentImpl "o_mall_backend/modules/payment/services/impl"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/models"
	"strconv"
	"time"

	"github.com/beego/beego/v2/core/logs"
)

// GetOrderStatisticsByMerchantID 获取商家订单统计数据
func (s *takeoutOrderService) GetOrderStatisticsByMerchantID(merchantID int64) (totalCount, completedCount, processingCount, cancelledCount int, err error) {
	// 参数校验
	if merchantID <= 0 {
		logs.Error("参数错误: 商家ID不能为空")
		return 0, 0, 0, 0, result.ErrInvalidParams
	}

	// 数据库表未准备好，暂时返回静态数据以保证API能正常响应
	logs.Warn("订单表未准备好，返回静态数据")

	// 根据商家ID生成一些模拟数据
	baseCount := int(merchantID) % 10
	if baseCount == 0 {
		baseCount = 5
	}

	// 模拟各种统计数据
	totalCount = baseCount * 10
	completedCount = baseCount * 5
	processingCount = baseCount * 3
	cancelledCount = baseCount * 2

	logs.Info("返回商家 %d 的模拟订单统计数据: 总数=%d, 完成=%d, 处理中=%d, 取消=%d",
		merchantID, totalCount, completedCount, processingCount, cancelledCount)

	return totalCount, completedCount, processingCount, cancelledCount, nil
}

// CreateOrderPayment 创建订单支付
func (s *takeoutOrderService) CreateOrderPayment(userID int64, req *dto.CreateTakeoutPaymentRequest) (*dto.TakeoutPaymentResponse, error) {
	logs.Info("[外卖订单服务] 开始创建订单支付 - 用户ID: %d, 订单ID: %d, 支付方式: %d",
		userID, req.OrderID, req.PaymentMethod)

	// 参数校验
	logs.Info("[外卖订单服务] 步骤1: 参数校验")
	if req.OrderID <= 0 {
		logs.Error("[外卖订单服务] 参数校验失败: 订单ID不能为空 - 订单ID: %d", req.OrderID)
		return nil, result.ErrInvalidParams
	}

	if req.PaymentMethod <= 0 {
		logs.Error("[外卖订单服务] 参数校验失败: 支付方式不能为空")
		return nil, result.ErrInvalidParams
	}
	logs.Info("[外卖订单服务] 参数校验通过")

	// 创建上下文
	ctx := context.Background()

	// 获取订单信息
	logs.Info("[外卖订单服务] 步骤2: 获取订单信息 - 订单ID: %d", req.OrderID)
	baseOrder, err := s.baseOrderSvc.GetOrder(ctx, req.OrderID)
	if err != nil {
		logs.Error("[外卖订单服务] 获取订单信息失败: %v, 订单ID: %d", err, req.OrderID)
		return nil, err
	}
	logs.Info("[外卖订单服务] 订单信息获取成功 - 订单ID: %d, 状态: %d, 支付状态: %d, 支付金额: %.2f",
		baseOrder.ID, baseOrder.Status, baseOrder.PayStatus, baseOrder.PayAmount)

	// 检查订单是否属于当前用户
	logs.Info("[外卖订单服务] 步骤3: 检查订单所有权 - 订单ID: %d", req.OrderID)
	if baseOrder.UserID != userID {
		logs.Error("[外卖订单服务] 订单不属于当前用户, 订单ID: %d, 订单用户ID: %d, 请求用户ID: %d",
			req.OrderID, baseOrder.UserID, userID)
		return nil, result.NewError(403, "无权操作此订单")
	}
	logs.Info("[外卖订单服务] 订单所有权验证通过")

	// 检查订单状态 - 使用Status字段
	logs.Info("[外卖订单服务] 步骤4: 检查订单状态 - 订单ID: %d, 当前状态: %d", req.OrderID, baseOrder.Status)
	if baseOrder.Status != 10 { // 10表示待付款状态
		logs.Error("[外卖订单服务] 订单状态不允许支付, 订单ID: %d, 当前状态: %d, 期望状态: 10",
			req.OrderID, baseOrder.Status)
		return nil, result.NewError(400, "订单状态不允许支付")
	}
	logs.Info("[外卖订单服务] 订单状态检查通过")

	// 检查订单支付状态 - 使用PayStatus字段
	logs.Info("[外卖订单服务] 步骤5: 检查订单支付状态 - 订单ID: %d, 当前支付状态: %d", req.OrderID, baseOrder.PayStatus)
	if baseOrder.PayStatus != 0 { // 0表示未支付状态
		logs.Error("[外卖订单服务] 订单已支付或已取消, 订单ID: %d, 支付状态: %d", req.OrderID, baseOrder.PayStatus)
		return nil, result.NewError(400, "订单已支付或已取消")
	}
	logs.Info("[外卖订单服务] 订单支付状态检查通过")

	// 构造支付创建请求
	logs.Info("[外卖订单服务] 步骤6: 构造支付创建请求 - 订单ID: %d", req.OrderID)

	// 将订单模块的支付方式常量转换为支付模块的枚举
	var paymentMethod paymentModels.PaymentMethod
	switch req.PaymentMethod {
	case 1: // 微信支付
		paymentMethod = paymentModels.PaymentMethodWechat
	case 2: // 支付宝
		paymentMethod = paymentModels.PaymentMethodAlipay
	case 3: // 余额支付
		paymentMethod = paymentModels.PaymentMethodBalance
	default:
		logs.Error("[外卖订单服务] 不支持的支付方式: %d", req.PaymentMethod)
		return nil, result.NewError(400, "不支持的支付方式")
	}

	paymentReq := &paymentDto.PaymentCreateRequest{
		OrderID:    req.OrderID,
		UserID:     userID,
		Amount:     baseOrder.PayAmount,
		Method:     paymentMethod,
		ClientIP:   req.ClientIP,
		DeviceInfo: req.DeviceInfo,
		ReturnURL:  req.ReturnURL,
		Remark:     req.Remark,
	}
	logs.Info("[外卖订单服务] 支付创建请求构造完成 - 订单ID: %d, 用户ID: %d, 金额: %.2f, 支付方式: %d",
		paymentReq.OrderID, paymentReq.UserID, paymentReq.Amount, paymentReq.Method)

	// 调用支付服务创建支付
	logs.Info("[外卖订单服务] 步骤7: 调用支付服务创建支付 - 订单ID: %d", req.OrderID)
	paymentSvc := paymentServices.NewPaymentService()

	// 确保支付服务设置了外卖订单的支付回调处理器
	if paymentServiceImpl, ok := paymentSvc.(*paymentImpl.PaymentServiceImpl); ok {
		callback := NewTakeoutPaymentCallback()
		paymentServiceImpl.SetOrderPaymentCallback(callback)
		logs.Info("[外卖订单服务] 已设置外卖支付回调处理器")
	}

	paymentResp, err := paymentSvc.CreatePayment(paymentReq)
	if err != nil {
		logs.Error("[外卖订单服务] 创建支付失败: %v, 订单ID: %d, 用户ID: %d", err, req.OrderID, userID)
		return nil, err
	}
	logs.Info("[外卖订单服务] 支付服务调用成功 - 订单ID: %d, 支付ID: %d", req.OrderID, paymentResp.PaymentID)

	// 构造响应
	logs.Info("[外卖订单服务] 步骤8: 构造响应数据 - 订单ID: %d, 支付ID: %d", req.OrderID, paymentResp.PaymentID)
	resp := &dto.TakeoutPaymentResponse{
		PaymentID:     paymentResp.PaymentID,
		TransactionNo: paymentResp.TransactionNo,
		PaymentURL:    paymentResp.PaymentURL,
		QrCodeURL:     paymentResp.QrCodeURL,
		AppPayParams:  paymentResp.AppPayParams,
		WebPayParams:  paymentResp.WebPayParams,
		ExpireTime:    paymentResp.ExpireTime,
	}

	logs.Info("[外卖订单服务] 订单支付创建完成 - 订单ID: %d, 用户ID: %d, 支付ID: %d, 交易流水号: %s",
		req.OrderID, userID, paymentResp.PaymentID, paymentResp.TransactionNo)
	return resp, nil
}

// QueryOrderPaymentStatus 查询订单支付状态
func (s *takeoutOrderService) QueryOrderPaymentStatus(userID int64, orderID int64) (*dto.TakeoutPaymentStatusResponse, error) {
	// 参数校验
	if orderID <= 0 {
		logs.Error("参数错误: 订单ID不能为空")
		return nil, result.ErrInvalidParams
	}

	// 创建上下文
	ctx := context.Background()

	// 获取订单信息
	baseOrder, err := s.baseOrderSvc.GetOrder(ctx, orderID)
	if err != nil {
		logs.Error("获取订单信息失败: %v, 订单ID: %d", err, orderID)
		return nil, err
	}

	// 检查订单是否属于当前用户
	if baseOrder.UserID != userID {
		logs.Error("订单不属于当前用户, 订单ID: %d, 用户ID: %d", orderID, userID)
		return nil, result.NewError(403, "无权操作此订单")
	}

	// 构造支付查询请求
	paymentReq := &paymentDto.PaymentQueryRequest{
		OrderID: orderID,
	}

	// 调用支付服务查询支付状态
	paymentSvc := paymentServices.NewPaymentService()
	paymentResp, err := paymentSvc.QueryPayment(paymentReq)
	if err != nil {
		logs.Error("查询支付状态失败: %v, 订单ID: %d, 用户ID: %d", err, orderID, userID)
		return nil, err
	}

	// 构造状态文本
	statusText := ""
	switch paymentResp.Status {
	case paymentModels.PaymentStatusPending:
		statusText = "待支付"
	case paymentModels.PaymentStatusSuccess:
		statusText = "支付成功"
	case paymentModels.PaymentStatusFailed:
		statusText = "支付失败"
	case paymentModels.PaymentStatusCancelled:
		statusText = "已取消"
	default:
		statusText = "未知状态"
	}

	// 构造响应
	resp := &dto.TakeoutPaymentStatusResponse{
		PaymentID:     paymentResp.PaymentID,
		TransactionNo: paymentResp.TransactionNo,
		OrderID:       paymentResp.OrderID,
		Amount:        paymentResp.Amount,
		PaymentMethod: int(paymentResp.Method),
		PaymentStatus: int(paymentResp.Status),
		PaymentTime:   paymentResp.PaymentTime,
		ExpireTime:    paymentResp.ExpireTime,
		StatusText:    statusText,
	}

	logs.Info("成功查询订单支付状态, 订单ID: %d, 用户ID: %d, 支付状态: %d", orderID, userID, paymentResp.Status)
	return resp, nil
}

// CloseOrderPayment 关闭订单支付
func (s *takeoutOrderService) CloseOrderPayment(userID int64, req *dto.CloseTakeoutPaymentRequest) (bool, error) {
	// 参数校验
	if req.OrderID <= 0 {
		logs.Error("参数错误: 订单ID不能为空")
		return false, result.ErrInvalidParams
	}

	// 创建上下文
	ctx := context.Background()

	// 获取订单信息
	baseOrder, err := s.baseOrderSvc.GetOrder(ctx, req.OrderID)
	if err != nil {
		logs.Error("获取订单信息失败: %v, 订单ID: %d", err, req.OrderID)
		return false, err
	}

	// 检查订单是否属于当前用户
	if baseOrder.UserID != userID {
		logs.Error("订单不属于当前用户, 订单ID: %d, 用户ID: %d", req.OrderID, userID)
		return false, result.NewError(403, "无权操作此订单")
	}

	// 检查订单状态 - 使用Status字段
	if baseOrder.Status != 10 { // 10表示待付款状态
		logs.Error("订单状态不允许关闭支付, 订单ID: %d, 状态: %d", req.OrderID, baseOrder.Status)
		return false, result.NewError(400, "订单状态不允许关闭支付")
	}

	// 检查订单支付状态 - 使用PayStatus字段
	if baseOrder.PayStatus != 0 { // 0表示未支付状态
		logs.Error("订单支付状态不允许关闭, 订单ID: %d, 支付状态: %d", req.OrderID, baseOrder.PayStatus)
		return false, result.NewError(400, "订单支付状态不允许关闭")
	}

	// 构造支付取消请求
	paymentReq := &paymentDto.PaymentCancelRequest{
		UserID: userID,
		Reason: req.Reason,
	}

	// 查询支付记录
	paymentQueryReq := &paymentDto.PaymentQueryRequest{
		OrderID: req.OrderID,
	}

	// 调用支付服务查询支付记录
	paymentSvc := paymentServices.NewPaymentService()
	paymentResp, err := paymentSvc.QueryPayment(paymentQueryReq)
	if err != nil {
		logs.Error("查询支付记录失败: %v, 订单ID: %d, 用户ID: %d", err, req.OrderID, userID)
		return false, err
	}

	// 设置交易流水号
	paymentReq.TransactionNo = paymentResp.TransactionNo

	// 调用支付服务关闭支付
	success, err := paymentSvc.CancelPayment(paymentReq)
	if err != nil {
		logs.Error("关闭支付失败: %v, 订单ID: %d, 用户ID: %d", err, req.OrderID, userID)
		return false, err
	}

	logs.Info("成功关闭订单支付, 订单ID: %d, 用户ID: %d, 结果: %t", req.OrderID, userID, success)
	return success, nil
}

// CreateBatchOrderPayment 创建多订单合并支付
func (s *takeoutOrderService) CreateBatchOrderPayment(userID int64, req *dto.CreateBatchTakeoutPaymentRequest) (*dto.BatchTakeoutPaymentResponse, error) {
	logs.Info("[外卖订单服务] 开始创建多订单合并支付 - 用户ID: %d, 订单数量: %d, 支付方式: %d",
		userID, len(req.OrderIDs), req.PaymentMethod)

	// 参数校验
	logs.Info("[外卖订单服务] 步骤1: 参数校验")
	if len(req.OrderIDs) == 0 {
		logs.Error("[外卖订单服务] 参数校验失败: 订单ID列表不能为空")
		return nil, result.ErrInvalidParams
	}

	if req.PaymentMethod <= 0 {
		logs.Error("[外卖订单服务] 参数校验失败: 支付方式不能为空")
		return nil, result.ErrInvalidParams
	}
	logs.Info("[外卖订单服务] 参数校验通过")

	// 创建上下文
	ctx := context.Background()

	// 获取所有订单信息并校验
	logs.Info("[外卖订单服务] 步骤2: 获取并校验所有订单信息")
	var totalAmount float64

	for _, orderID := range req.OrderIDs {
		// 获取订单信息
		baseOrder, err := s.baseOrderSvc.GetOrder(ctx, orderID)
		if err != nil {
			logs.Error("[外卖订单服务] 获取订单信息失败: %v, 订单ID: %d", err, orderID)
			return nil, err
		}

		// 检查订单是否属于当前用户
		if baseOrder.UserID != userID {
			logs.Error("[外卖订单服务] 订单不属于当前用户, 订单ID: %d, 订单用户ID: %d, 请求用户ID: %d",
				orderID, baseOrder.UserID, userID)
			return nil, result.NewError(403, fmt.Sprintf("无权操作订单%d", orderID))
		}

		// 检查订单状态
		if baseOrder.Status != 10 { // 10表示待付款状态
			logs.Error("[外卖订单服务] 订单状态不允许支付, 订单ID: %d, 当前状态: %d, 期望状态: 10",
				orderID, baseOrder.Status)
			return nil, result.NewError(400, fmt.Sprintf("订单%d状态不允许支付", orderID))
		}

		// 检查订单支付状态
		if baseOrder.PayStatus != 0 { // 0表示未支付状态
			logs.Error("[外卖订单服务] 订单已支付或已取消, 订单ID: %d, 支付状态: %d", orderID, baseOrder.PayStatus)
			return nil, result.NewError(400, fmt.Sprintf("订单%d已支付或已取消", orderID))
		}

		totalAmount += baseOrder.PayAmount
		logs.Info("[外卖订单服务] 订单校验通过 - 订单ID: %d, 金额: %.2f", orderID, baseOrder.PayAmount)
	}

	logs.Info("[外卖订单服务] 所有订单校验通过，总金额: %.2f", totalAmount)

	// 构造支付创建请求
	logs.Info("[外卖订单服务] 步骤3: 构造批量支付创建请求")

	// 将订单模块的支付方式常量转换为支付模块的枚举
	var paymentMethod paymentModels.PaymentMethod
	switch req.PaymentMethod {
	case 1: // 微信支付
		paymentMethod = paymentModels.PaymentMethodWechat
	case 2: // 支付宝
		paymentMethod = paymentModels.PaymentMethodAlipay
	case 3: // 余额支付
		paymentMethod = paymentModels.PaymentMethodBalance
	default:
		logs.Error("[外卖订单服务] 不支持的支付方式: %d", req.PaymentMethod)
		return nil, result.NewError(400, "不支持的支付方式")
	}

	paymentReq := &paymentDto.PaymentCreateRequest{
		OrderID:    req.OrderIDs[0], // 使用第一个订单ID作为主订单ID
		UserID:     userID,
		Amount:     totalAmount,
		Method:     paymentMethod,
		ClientIP:   req.ClientIP,
		DeviceInfo: req.DeviceInfo,
		ReturnURL:  req.ReturnURL,
		Remark:     fmt.Sprintf("合并支付-%s", req.Remark),
	}
	logs.Info("[外卖订单服务] 批量支付创建请求构造完成 - 主订单ID: %d, 用户ID: %d, 总金额: %.2f, 支付方式: %d",
		paymentReq.OrderID, paymentReq.UserID, paymentReq.Amount, paymentReq.Method)

	// 调用支付服务创建支付
	logs.Info("[外卖订单服务] 步骤4: 调用支付服务创建批量支付")
	paymentSvc := paymentServices.NewPaymentService()

	// 确保支付服务设置了外卖订单的支付回调处理器
	if paymentServiceImpl, ok := paymentSvc.(*paymentImpl.PaymentServiceImpl); ok {
		callback := NewTakeoutPaymentCallback()
		paymentServiceImpl.SetOrderPaymentCallback(callback)
		logs.Info("[外卖订单服务] 已设置外卖支付回调处理器（批量支付）")
	}

	paymentResp, err := paymentSvc.CreatePayment(paymentReq)
	if err != nil {
		logs.Error("[外卖订单服务] 创建批量支付失败: %v, 用户ID: %d, 订单数量: %d", err, userID, len(req.OrderIDs))
		return nil, err
	}
	logs.Info("[外卖订单服务] 批量支付服务调用成功 - 支付ID: %d", paymentResp.PaymentID)

	// 构造响应
	logs.Info("[外卖订单服务] 步骤5: 构造响应数据")
	resp := &dto.BatchTakeoutPaymentResponse{
		BatchPaymentID: paymentResp.PaymentID,
		OrderIDs:       req.OrderIDs,
		TotalAmount:    totalAmount,
		TransactionNo:  paymentResp.TransactionNo,
		PaymentURL:     paymentResp.PaymentURL,
		QrCodeURL:      paymentResp.QrCodeURL,
		AppPayParams:   paymentResp.AppPayParams,
		WebPayParams:   paymentResp.WebPayParams,
		ExpireTime:     paymentResp.ExpireTime,
	}

	logs.Info("[外卖订单服务] 多订单合并支付创建完成 - 用户ID: %d, 批量支付ID: %d, 订单数量: %d, 总金额: %.2f, 交易流水号: %s",
		userID, paymentResp.PaymentID, len(req.OrderIDs), totalAmount, paymentResp.TransactionNo)
	return resp, nil
}

// MerchantProcessRefund 商家处理退款申请
func (s *takeoutOrderService) MerchantProcessRefund(merchantID int64, req *dto.MerchantProcessRefundRequest) (*dto.MerchantProcessRefundResponse, error) {
	logs.Info("[商家处理退款] 开始处理 - 商家ID: %d, 退款ID: %s, 操作: %s", merchantID, req.RefundID, req.Action)

	// 1. 获取退款记录
	var refund *models.TakeoutRefund
	var err error

	// 尝试将RefundID转换为数字ID，如果失败则当作退款单号处理
	if refundID, parseErr := strconv.ParseInt(req.RefundID, 10, 64); parseErr == nil {
		// 按退款ID查询
		refund, err = s.refundRepo.GetRefundByID(refundID)
	} else {
		// 按退款单号查询
		refund, err = s.refundRepo.GetRefundByRefundNo(req.RefundID)
	}

	if err != nil {
		logs.Error("[商家处理退款] 获取退款记录失败: %v", err)
		return nil, fmt.Errorf("获取退款记录失败: %v", err)
	}

	if refund == nil {
		logs.Error("[商家处理退款] 退款记录不存在: %s", req.RefundID)
		return nil, result.NewError(404, "退款记录不存在")
	}

	// 2. 验证退款状态
	if refund.RefundStatus != models.RefundStatusApplying {
		logs.Error("[商家处理退款] 退款状态不正确 - 当前状态: %d", refund.RefundStatus)
		return nil, result.NewError(400, "退款申请状态不正确，无法处理")
	}

	// 3. 获取订单信息验证商家权限
	order, err := s.orderRepo.GetByOrderID(refund.OrderID)
	if err != nil {
		logs.Error("[商家处理退款] 获取订单信息失败: %v", err)
		return nil, fmt.Errorf("获取订单信息失败: %v", err)
	}

	if order.MerchantID != merchantID {
		logs.Error("[商家处理退款] 商家权限验证失败 - 订单商家ID: %d, 当前商家ID: %d", order.MerchantID, merchantID)
		return nil, result.NewError(403, "无权限处理此退款申请")
	}

	now := time.Now()

	if req.Action == "approve" {
		// 同意退款
		logs.Info("[商家处理退款] 同意退款 - 退款金额: %.2f", refund.RefundAmount)

		// 验证退款合理性
		if refund.RefundAmount <= 0 {
			logs.Error("[商家处理退款] 退款金额无效: %.2f", refund.RefundAmount)
			return nil, result.NewError(400, "退款金额无效")
		}

		// 查询支付记录获取PaymentID
		logs.Info("[商家处理退款] 查询支付记录 - 订单ID: %d", refund.OrderID)
		payment, err := s.paymentRepo.GetPaymentByOrderID(refund.OrderID)
		if err != nil {
			logs.Error("[商家处理退款] 查询支付记录失败: %v", err)
			return nil, fmt.Errorf("查询支付记录失败: %v", err)
		}

		if payment == nil {
			logs.Error("[商家处理退款] 支付记录不存在 - 订单ID: %d", refund.OrderID)
			return nil, fmt.Errorf("支付记录不存在")
		}

		logs.Info("[商家处理退款] 找到支付记录 - 支付ID: %d, 状态: %d, 金额: %.2f, 支付方式: %d",
			payment.ID, int(payment.Status), payment.Amount, int(payment.Method))

		// 验证退款金额不能超过支付金额
		if refund.RefundAmount > payment.Amount {
			logs.Error("[商家处理退款] 退款金额超过支付金额 - 退款金额: %.2f, 支付金额: %.2f", refund.RefundAmount, payment.Amount)
			return nil, result.NewError(400, "退款金额不能超过支付金额")
		}

		// 调用支付模块创建退款
		logs.Info("[商家处理退款] 调用支付模块创建退款 - 订单ID: %d, 支付ID: %d, 退款金额: %.2f", refund.OrderID, payment.ID, refund.RefundAmount)
		refundCreateReq := &paymentDto.RefundCreateRequest{
			PaymentID:    payment.ID,
			OrderID:      refund.OrderID,
			UserID:       refund.UserID,
			Amount:       refund.RefundAmount,
			Reason:       refund.RefundReason,
			OperatorID:   merchantID,
			NeedApproval: false, // 商家已经审批，不需要再次审批
		}

		refundCreateResp, err := s.refundSvc.CreateRefund(refundCreateReq)
		if err != nil {
			logs.Error("[商家处理退款] 调用支付模块创建退款失败: %v", err)
			return nil, fmt.Errorf("创建退款失败: %v", err)
		}

		logs.Info("[商家处理退款] 支付模块退款创建成功 - 退款ID: %d, 支付退款ID: %d, 状态: %d", refund.ID, refundCreateResp.RefundID, refundCreateResp.Status)

		// 根据支付模块返回的状态决定后续处理
		if refundCreateResp.Status == 1 { // 退款成功
			// 更新退款记录状态为已完成
			refund.RefundStatus = models.RefundStatusCompleted
			refund.ProcessTime = &now
			refund.CompleteTime = &now
			refund.ProcessRemark = req.ProcessRemark
			refund.ProcessorID = merchantID // 设置处理人ID

			err = s.refundRepo.UpdateRefund(refund)
			if err != nil {
				logs.Error("[商家处理退款] 更新退款记录失败: %v", err)
				return nil, fmt.Errorf("更新退款记录失败: %v", err)
			}

			// 更新订单状态为已退款
			ctx := context.Background()
			err = s.baseOrderRepo.UpdateOrderStatus(ctx, refund.OrderID, 80) // OrderStatusRefunded = 80
			if err != nil {
				logs.Error("[商家处理退款] 更新订单状态失败: %v", err)
				// 不返回错误，因为退款已经成功，只是订单状态更新失败
			} else {
				logs.Info("[商家处理退款] 订单状态已更新为已退款 - 订单ID: %d", refund.OrderID)
			}

			// 还原优惠券状态
			logs.Info("[商家处理退款] 开始还原优惠券状态 - 订单ID: %d", refund.OrderID)
			refundSvc := paymentServices.NewRefundService()
			err = refundSvc.RestoreCouponsForRefund(refund.OrderID)
			if err != nil {
				logs.Error("[商家处理退款] 还原优惠券状态失败: %v", err)
				// 不返回错误，因为退款已经成功，只是优惠券还原失败
			} else {
				logs.Info("[商家处理退款] 优惠券状态还原成功 - 订单ID: %d", refund.OrderID)
			}

			logs.Info("[商家处理退款] 同意退款处理完成 - 退款ID: %d, 退款金额: %.2f, 支付退款ID: %d", refund.ID, refund.RefundAmount, refundCreateResp.RefundID)
		} else if refundCreateResp.Status == 2 { // 退款失败
			logs.Error("[商家处理退款] 支付模块退款失败 - 消息: %s", refundCreateResp.Message)
			return nil, fmt.Errorf("退款失败: %s", refundCreateResp.Message)
		} else { // 处理中或其他状态
			// 更新退款记录状态为商家已同意，等待退款完成
			refund.RefundStatus = models.RefundStatusApproved
			refund.ProcessTime = &now
			refund.ProcessRemark = req.ProcessRemark
			refund.ProcessorID = merchantID // 设置处理人ID

			err = s.refundRepo.UpdateRefund(refund)
			if err != nil {
				logs.Error("[商家处理退款] 更新退款记录失败: %v", err)
				return nil, fmt.Errorf("更新退款记录失败: %v", err)
			}

			logs.Info("[商家处理退款] 退款申请已同意，等待支付平台处理 - 退款ID: %d, 支付退款ID: %d", refund.ID, refundCreateResp.RefundID)
		}

	} else if req.Action == "reject" {
		// 拒绝退款
		logs.Info("[商家处理退款] 拒绝退款 - 拒绝理由: %s", req.ProcessRemark)

		// 验证拒绝理由不能为空
		if req.ProcessRemark == "" {
			logs.Error("[商家处理退款] 拒绝退款时必须提供拒绝理由")
			return nil, result.NewError(400, "拒绝退款时必须提供拒绝理由")
		}

		// 更新退款记录状态为已拒绝
		refund.RefundStatus = models.RefundStatusRejected
		refund.ProcessTime = &now
		refund.ProcessRemark = req.ProcessRemark
		refund.ProcessorID = merchantID // 设置处理人ID

		err = s.refundRepo.UpdateRefund(refund)
		if err != nil {
			logs.Error("[商家处理退款] 更新退款拒绝状态失败: %v", err)
			return nil, fmt.Errorf("更新退款拒绝状态失败: %v", err)
		}

		logs.Info("[商家处理退款] 拒绝退款处理完成 - 退款ID: %d, 处理人ID: %d", refund.ID, merchantID)

	} else {
		return nil, result.NewError(400, fmt.Sprintf("无效的操作类型: %s", req.Action))
	}

	// 构造响应
	response := &dto.MerchantProcessRefundResponse{
		RefundID:      refund.ID,
		OrderID:       refund.OrderID,
		RefundNo:      refund.RefundNo,
		RefundStatus:  refund.RefundStatus,
		ProcessRemark: refund.ProcessRemark,
		StatusText:    s.getProcessMessage(req.Action, refund.RefundStatus),
	}

	if refund.ProcessTime != nil {
		response.ProcessTime = *refund.ProcessTime
	}

	// 异步发送退款处理结果通知给用户
	go func() {
		err := s.sendRefundProcessNotification(context.Background(), refund, order, req.Action)
		if err != nil {
			logs.Error("[商家处理退款] 发送退款处理通知失败: %v, 退款ID: %d", err, refund.ID)
		}
	}()

	// 清除用户订单列表缓存，确保退款处理结果能及时反映在订单列表中
	go func() {
		cacheCtx := context.Background()
		if err := s.cacheService.ClearUserOrderListCache(cacheCtx, refund.UserID); err != nil {
			logs.Error("[商家处理退款] 清除用户订单列表缓存失败: %v, 用户ID: %d", err, refund.UserID)
		} else {
			logs.Info("[商家处理退款] 成功清除用户订单列表缓存 - 用户ID: %d, 订单ID: %d", refund.UserID, refund.OrderID)
		}
	}()

	logs.Info("[商家处理退款] 处理完成 - 响应: %+v", response)
	return response, nil
}

// getProcessMessage 获取处理结果消息
func (s *takeoutOrderService) getProcessMessage(action string, status int) string {
	if action == "approve" {
		if status == models.RefundStatusCompleted {
			return "退款申请已同意，款项已退回用户账户"
		}
		return "退款申请已同意，正在处理中"
	} else if action == "reject" {
		return "退款申请已拒绝"
	}
	return "处理完成"
}

// ApplyRefund 申请退款
func (s *takeoutOrderService) ApplyRefund(userID int64, req *dto.ApplyRefundRequest) (*dto.RefundResponse, error) {
	logs.Info("[外卖订单服务] 开始申请退款 - 用户ID: %d, 订单ID: %d, 退款原因: %s", userID, req.OrderID, req.Reason)

	// 参数校验
	if req.OrderID <= 0 {
		logs.Error("[外卖订单服务] 参数校验失败: 订单ID不能为空")
		return nil, result.ErrInvalidParams
	}

	if req.Reason == "" {
		logs.Error("[外卖订单服务] 参数校验失败: 退款原因不能为空")
		return nil, result.ErrInvalidParams
	}

	// 创建上下文
	ctx := context.Background()

	// 获取订单信息
	logs.Info("[外卖订单服务] 步骤1: 获取订单信息 - 订单ID: %d", req.OrderID)
	baseOrder, err := s.baseOrderSvc.GetOrder(ctx, req.OrderID)
	if err != nil {
		logs.Error("[外卖订单服务] 获取订单信息失败: %v, 订单ID: %d", err, req.OrderID)
		return nil, err
	}

	// 检查订单是否属于当前用户
	if baseOrder.UserID != userID {
		logs.Error("[外卖订单服务] 订单不属于当前用户, 订单ID: %d, 订单用户ID: %d, 请求用户ID: %d",
			req.OrderID, baseOrder.UserID, userID)
		return nil, result.NewError(403, "无权操作此订单")
	}

	// 检查订单状态是否允许退款（已支付且未完成配送）
	if baseOrder.PayStatus != orderConstants.PayStatusPaid {
		logs.Error("[外卖订单服务] 订单未支付，不允许退款, 订单ID: %d, 支付状态: %d", req.OrderID, baseOrder.PayStatus)
		return nil, result.NewError(400, "订单未支付，不允许退款")
	}

	// 检查是否已经申请过退款
	existingRefund, err := s.refundRepo.GetRefundByOrderID(req.OrderID)
	if err != nil {
		logs.Error("[外卖订单服务] 查询退款记录失败: %v, 订单ID: %d", err, req.OrderID)
		return nil, err
	}

	if existingRefund != nil {
		logs.Error("[外卖订单服务] 订单已申请过退款, 订单ID: %d, 退款ID: %d", req.OrderID, existingRefund.ID)
		return nil, result.NewError(400, "订单已申请过退款")
	}

	// 获取外卖订单扩展信息
	takeoutOrder, err := s.orderRepo.GetByOrderID(req.OrderID)
	if err != nil {
		logs.Error("[外卖订单服务] 获取外卖订单信息失败: %v, 订单ID: %d", err, req.OrderID)
		return nil, err
	}

	// 确定退款金额（如果未指定则使用订单实付金额）
	refundAmount := req.Amount
	if refundAmount <= 0 {
		refundAmount = baseOrder.PayAmount
	}

	// 生成退款单号
	refundNo := fmt.Sprintf("RF%d%d", time.Now().Unix(), req.OrderID)

	// 创建退款记录
	refund := &models.TakeoutRefund{
		RefundNo:     refundNo,
		OrderID:      req.OrderID,
		UserID:       userID,
		MerchantID:   takeoutOrder.MerchantID,
		RefundAmount: refundAmount,
		RefundReason: req.Reason,
		RefundRemark: req.Remark,
		RefundStatus: models.RefundStatusApplying, // 申请中
		ApplyTime:    time.Now(),
	}

	// 保存退款记录
	logs.Info("[外卖订单服务] 步骤2: 创建退款记录 - 订单ID: %d, 退款金额: %.2f", req.OrderID, refundAmount)
	err = s.refundRepo.CreateRefund(refund)
	if err != nil {
		logs.Error("[外卖订单服务] 创建退款记录失败: %v, 订单ID: %d", err, req.OrderID)
		return nil, err
	}

	// 步骤3: 更新订单状态为退款中
	logs.Info("[外卖订单服务] 步骤3: 更新订单状态为退款中 - 订单ID: %d", req.OrderID)
	err = s.baseOrderRepo.UpdateOrderStatus(ctx, req.OrderID, orderConstants.OrderStatusRefunding)
	if err != nil {
		logs.Error("[外卖订单服务] 更新订单状态失败: %v, 订单ID: %d", err, req.OrderID)
		// 注意：这里不返回错误，因为退款记录已经创建成功，订单状态可以后续手动修复
		logs.Warn("[外卖订单服务] 退款记录已创建，但订单状态更新失败，需要手动处理 - 订单ID: %d, 退款ID: %d", req.OrderID, refund.ID)
	} else {
		logs.Info("[外卖订单服务] 订单状态已更新为退款中 - 订单ID: %d", req.OrderID)
	}

	// 构造响应
	resp := &dto.RefundResponse{
		RefundID:      refund.ID,
		OrderID:       refund.OrderID,
		RefundNo:      refund.RefundNo,
		RefundAmount:  refund.RefundAmount,
		RefundStatus:  refund.RefundStatus,
		RefundReason:  refund.RefundReason,
		ApplyTime:     refund.ApplyTime,
		ProcessTime:   refund.ProcessTime,
		CompleteTime:  refund.CompleteTime,
		StatusText:    models.GetRefundStatusText(refund.RefundStatus),
		ProcessRemark: refund.ProcessRemark,
	}

	// 异步发送退款申请通知给商家
	go func() {
		err := s.sendRefundApplicationNotification(context.Background(), refund, baseOrder, takeoutOrder)
		if err != nil {
			logs.Error("[外卖订单服务] 发送退款申请通知失败: %v, 退款ID: %d", err, refund.ID)
		}
	}()

	// 清除用户订单列表缓存，确保退款状态变更能及时反映在订单列表中
	go func() {
		cacheCtx := context.Background()
		if err := s.cacheService.ClearUserOrderListCache(cacheCtx, userID); err != nil {
			logs.Error("[外卖订单服务] 清除用户订单列表缓存失败: %v, 用户ID: %d", err, userID)
		} else {
			logs.Info("[外卖订单服务] 成功清除用户订单列表缓存 - 用户ID: %d, 订单ID: %d", userID, req.OrderID)
		}
	}()

	logs.Info("[外卖订单服务] 退款申请成功 - 用户ID: %d, 订单ID: %d, 退款ID: %d, 退款单号: %s",
		userID, req.OrderID, refund.ID, refund.RefundNo)
	return resp, nil
}

// sendRefundApplicationNotification 发送退款申请通知给商家
func (s *takeoutOrderService) sendRefundApplicationNotification(ctx context.Context, refund *models.TakeoutRefund, baseOrder *orderDto.OrderResponse, takeoutOrder *models.TakeoutOrderExtension) error {
	logs.Info("[外卖订单服务] ========== 开始发送退款申请通知 ===========")
	logs.Info("[外卖订单服务] 退款ID: %d, 订单ID: %d, 商家ID: %d, 退款金额: %.2f",
		refund.ID, refund.OrderID, refund.MerchantID, refund.RefundAmount)

	// 获取新的商家WebSocket通知服务
	container := chatServices.GetServiceContainer()
	merchantService := container.GetWebSocketMerchantService()

	if merchantService == nil {
		logs.Warn("[外卖订单服务] 商家WebSocket通知服务未初始化，跳过退款申请通知发送")
		return nil
	}

	// 使用新的统一WebSocket通知架构发送退款申请通知
	logs.Info("[外卖订单服务] 发送商家退款申请通知 - 商家ID: %d", refund.MerchantID)
	err := merchantService.SendRefundRequestNotification(
		ctx,
		refund.MerchantID,
		refund.ID,
		refund.RefundNo,
		refund.OrderID,
		baseOrder.OrderNo,
		refund.RefundAmount,
		refund.RefundReason,
	)

	if err != nil {
		logs.Error("[外卖订单服务] 发送商家退款申请通知失败: %v", err)
		return err
	} else {
		logs.Info("[外卖订单服务] 商家退款申请通知发送成功")
	}

	logs.Info("[外卖订单服务] 退款申请通知发送完成 - 退款ID: %d", refund.ID)
	return nil
}

// sendRefundProcessNotification 发送退款处理结果通知给用户
func (s *takeoutOrderService) sendRefundProcessNotification(ctx context.Context, refund *models.TakeoutRefund, takeoutOrder *models.TakeoutOrderExtension, action string) error {
	logs.Info("[外卖订单服务] ========== 开始发送退款处理结果通知 ===========")
	logs.Info("[外卖订单服务] 退款ID: %d, 订单ID: %d, 用户ID: %d, 处理动作: %s",
		refund.ID, refund.OrderID, refund.UserID, action)

	// 获取新的用户WebSocket通知服务
	container := chatServices.GetServiceContainer()
	userService := container.GetWebSocketUserService()

	if userService == nil {
		logs.Warn("[外卖订单服务] 用户WebSocket通知服务未初始化，跳过退款处理通知发送")
		return nil
	}

	// 获取基础订单信息
	logs.Info("[外卖订单服务] 尝试获取基础订单信息 - 订单ID: %d", refund.OrderID)
	baseOrder, err := s.baseOrderSvc.GetOrder(ctx, refund.OrderID)
	if err != nil {
		logs.Error("[外卖订单服务] 获取基础订单信息失败: %v, 订单ID: %d", err, refund.OrderID)
		// 如果基础订单不存在，尝试使用外卖订单信息构造基本信息
		if takeoutOrder != nil {
			logs.Warn("[外卖订单服务] 基础订单不存在，使用外卖订单信息构造订单号")
			// 生成一个临时的订单号用于通知
			orderNo := fmt.Sprintf("TO%d", refund.OrderID)
			baseOrder = &orderDto.OrderResponse{
				ID:      refund.OrderID,
				OrderNo: orderNo,
			}
		} else {
			return err
		}
	}

	// 根据处理动作确定退款状态
	var status, remark string
	if action == "approve" {
		if refund.RefundStatus == models.RefundStatusCompleted {
			status = "approved"
		} else {
			status = "processing"
		}
		remark = refund.ProcessRemark
	} else if action == "reject" {
		status = "rejected"
		remark = refund.ProcessRemark
	} else {
		logs.Error("[外卖订单服务] 未知的处理动作: %s", action)
		return fmt.Errorf("未知的处理动作: %s", action)
	}

	// 使用新的统一WebSocket通知架构发送退款结果通知
	logs.Info("[外卖订单服务] 发送用户退款处理结果通知 - 用户ID: %d, 动作: %s", refund.UserID, action)
	logs.Info("[外卖订单服务] 退款通知详细信息 - 退款ID: %d, 退款单号: %s, 订单ID: %d, 订单号: %s, 退款金额: %.2f, 状态: %s, 备注: %s",
		refund.ID, refund.RefundNo, refund.OrderID, baseOrder.OrderNo, refund.RefundAmount, status, remark)

	err = userService.SendRefundResultNotification(
		ctx,
		refund.UserID,
		refund.ID,
		refund.RefundNo,
		refund.OrderID,
		baseOrder.OrderNo,
		refund.RefundAmount,
		status,
		remark,
	)

	if err != nil {
		logs.Error("[外卖订单服务] 发送用户退款处理结果通知失败: %v", err)
		return err
	} else {
		logs.Info("[外卖订单服务] 用户退款处理结果通知发送成功 - 用户ID: %d, 退款ID: %d", refund.UserID, refund.ID)
	}

	logs.Info("[外卖订单服务] 退款处理结果通知发送完成 - 退款ID: %d, 动作: %s", refund.ID, action)
	return nil
}
