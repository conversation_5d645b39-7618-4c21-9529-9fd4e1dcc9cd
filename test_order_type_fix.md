# 外卖订单类型修复测试

## 问题描述

用户报告：使用 `/api/v1/user/takeout/order/create` 创建外卖订单后，该订单在 `/api/v1/orders/simple/high-performance` 中查询不到，但在 `/api/v1/user/takeout/order/list` 中可以查询到。

## 根本原因分析

经过深入分析，发现问题的根本原因是**订单类型设置不一致**：

### 1. 常量定义
在 `utils/constants/business.go` 中定义：
```go
const (
    OrderTypeNormal = 1      // 普通订单
    OrderTypeTakeout = 2     // 外卖订单
    OrderTypeDelivery = 3    // 跑腿订单
    OrderTypeGroup = 4       // 团购订单
    OrderTypeSecondKill = 5  // 秒杀订单
)
```

### 2. 修复前的问题
- **外卖订单创建时**：设置 `order_type = 1`（硬编码）
- **高性能查询**：当不传 `order_type` 参数时，查询所有类型订单
- **数据不一致**：外卖订单存储为 `order_type = 1`，但按常量定义应该是 `order_type = 2`

### 3. 修复方案
将外卖订单创建时的订单类型从硬编码的 `1` 改为使用统一常量 `constants.OrderTypeTakeout`（值为 `2`）。

## 修复内容

### 1. 修复外卖订单服务
**文件**: `modules/takeout/services/takeout_order_service.go`
```go
// 修复前
OrderType: 1, // 外卖订单类型为1

// 修复后  
OrderType: constants.OrderTypeTakeout, // 外卖订单类型，使用统一常量
```

### 2. 修复多商家订单服务
**文件**: `modules/takeout/services/multi_merchant_order_service.go`
```go
// 修复前
OrderType: 1, // 外卖订单类型

// 修复后
OrderType: constants.OrderTypeTakeout, // 外卖订单类型，使用统一常量
```

### 3. 添加必要的导入
在两个文件中都添加了：
```go
import "o_mall_backend/utils/constants"
```

## 测试步骤

### 1. 创建外卖订单
```bash
curl -X POST "http://localhost:8080/api/v1/user/takeout/order/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "takeoutAddressID": 1,
    "addressLat": 39.9042,
    "addressLng": 116.4074,
    "paymentMethodInt": 1,
    "clientIP": "*************",
    "deviceInfo": "Test Device",
    "merchantOrders": [{
      "merchantID": 1,
      "cartItemIDs": [1, 2],
      "deliveryTime": "2024-01-15 12:00:00"
    }]
  }'
```

### 2. 验证高性能订单列表（查询所有订单）
```bash
curl -X GET "http://localhost:8080/api/v1/orders/simple/high-performance?page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 验证高性能订单列表（只查询外卖订单）
```bash
curl -X GET "http://localhost:8080/api/v1/orders/simple/high-performance?order_type=2&page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 验证外卖订单列表
```bash
curl -X GET "http://localhost:8080/api/v1/user/takeout/order/list?page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 预期结果

修复后，新创建的外卖订单应该：
1. **在高性能订单列表中可见**（不传 order_type 参数时）
2. **在高性能订单列表中可见**（传 order_type=2 参数时）
3. **在外卖订单列表中可见**（原本就可见）
4. **订单类型显示为 "外卖订单"**

## 数据库验证

可以通过以下 SQL 查询验证订单类型：
```sql
SELECT id, order_no, order_type, status, created_at 
FROM `order` 
WHERE user_i_d = YOUR_USER_ID 
ORDER BY created_at DESC 
LIMIT 10;
```

修复后新创建的外卖订单，`order_type` 字段应该为 `2`。

## 影响范围

此修复影响：
1. **新创建的外卖订单**：订单类型将正确设置为 `2`
2. **高性能订单列表查询**：能正确查询到外卖订单
3. **订单类型显示**：外卖订单将正确显示为"外卖订单"
4. **数据一致性**：订单类型与常量定义保持一致

## 注意事项

1. **历史数据**：修复前创建的外卖订单仍然是 `order_type = 1`，不会自动更新
2. **向后兼容**：系统仍然能处理 `order_type = 1` 的历史外卖订单
3. **缓存清理**：如果有缓存，建议清理相关缓存以确保数据一致性

## 修复验证

修复成功的标志：
- [x] 编译无错误
- [ ] 新创建的外卖订单在高性能列表中可见
- [ ] 订单类型正确显示为"外卖订单"
- [ ] 数据库中 order_type 字段为 2
